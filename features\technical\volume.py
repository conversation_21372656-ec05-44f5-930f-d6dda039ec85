"""
Volume Technical Indicators Engine

Generates comprehensive volume-based technical indicators including:
- Volume Weighted Average Price (VWAP)
- On-Balance Volume (OBV)
- Volume spike detection
- Price-volume correlation analysis
- Volume profile features
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

# Import base classes
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from features.core.base_feature import BaseFeatureEngine, FeatureResult
from features.config.feature_config import FeatureConfig
from data_collection.config import Config


class VolumeIndicators(BaseFeatureEngine):
    """
    Volume technical indicators engine.
    
    Generates comprehensive volume-based indicators for XAUUSD trading
    including VWAP, OBV, and volume analysis features.
    """
    
    def __init__(self, config: Config, feature_config: FeatureConfig, **kwargs):
        """
        Initialize volume indicators engine.
        
        Args:
            config: Main system configuration
            feature_config: Feature-specific configuration
            **kwargs: Additional parameters
        """
        super().__init__(config)
        self.feature_config = feature_config
        self.technical_config = feature_config.technical_indicators
        
        # Volume indicators configuration
        self.volume_config = self.technical_config.get('volume_indicators', {})
        self.vwap_enabled = self.volume_config.get('vwap', True)
        self.obv_enabled = self.volume_config.get('obv', True)
        
        # Volume spike detection
        self.volume_spike_config = self.volume_config.get('volume_spikes', {})
        self.spike_threshold = self.volume_spike_config.get('z_score_threshold', 2.0)
        
        # Price-volume correlation
        self.pv_correlation_config = self.volume_config.get('price_volume_correlation', {})
        self.pv_windows = self.pv_correlation_config.get('windows', [20, 50])
        
        # Volume analysis windows
        self.volume_windows = [10, 20, 50]
    
    def get_feature_type(self) -> str:
        """Get the type of features this engine generates."""
        return "volume_indicators"
    
    def get_required_columns(self) -> List[str]:
        """Get required columns for volume indicators."""
        return ['open', 'high', 'low', 'close', 'volume']
    
    def get_feature_names(self) -> List[str]:
        """Get list of feature names this engine generates."""
        feature_names = []
        
        # VWAP features
        if self.vwap_enabled:
            for window in self.volume_windows:
                feature_names.extend([
                    f"vwap_{window}",
                    f"vwap_deviation_{window}",
                    f"price_above_vwap_{window}"
                ])
        
        # OBV features
        if self.obv_enabled:
            feature_names.extend([
                "obv",
                "obv_sma_10",
                "obv_sma_20",
                "obv_momentum"
            ])
        
        # Volume spike features
        feature_names.extend([
            "volume_spike",
            "volume_z_score",
            "volume_percentile"
        ])
        
        # Price-volume correlation features
        for window in self.pv_windows:
            feature_names.extend([
                f"price_volume_corr_{window}",
                f"volume_price_divergence_{window}"
            ])
        
        # Volume profile features
        for window in self.volume_windows:
            feature_names.extend([
                f"volume_sma_{window}",
                f"volume_ratio_{window}",
                f"volume_trend_{window}"
            ])
        
        return feature_names
    
    def _generate_features_impl(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Implementation-specific feature generation logic.
        
        Args:
            data: Input OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with generated volume indicator features
        """
        # Ensure datetime index
        data = self._ensure_datetime_index(data)
        
        # Initialize features DataFrame
        features = pd.DataFrame(index=data.index)
        
        # Check if volume data is available
        volume_col = self._get_volume_column(data)
        if volume_col is None:
            self.logger.warning("No volume data available, generating limited volume features")
            return features
        
        # Generate VWAP features
        if self.vwap_enabled:
            vwap_features = self._generate_vwap_features(data, volume_col)
            features = pd.concat([features, vwap_features], axis=1)
        
        # Generate OBV features
        if self.obv_enabled:
            obv_features = self._generate_obv_features(data, volume_col)
            features = pd.concat([features, obv_features], axis=1)
        
        # Generate volume spike features
        spike_features = self._generate_volume_spike_features(data, volume_col)
        features = pd.concat([features, spike_features], axis=1)
        
        # Generate price-volume correlation features
        correlation_features = self._generate_price_volume_correlation_features(data, volume_col)
        features = pd.concat([features, correlation_features], axis=1)
        
        # Generate volume profile features
        profile_features = self._generate_volume_profile_features(data, volume_col)
        features = pd.concat([features, profile_features], axis=1)
        
        self.logger.info(f"Generated {len(features.columns)} volume indicator features")
        return features
    
    def _get_volume_column(self, data: pd.DataFrame) -> Optional[str]:
        """Identify the volume column in the data."""
        volume_candidates = ['tick_volume', 'real_volume', 'volume']
        
        for col in volume_candidates:
            if col in data.columns and not data[col].isna().all():
                return col
        
        return None
    
    def _generate_vwap_features(self, data: pd.DataFrame, volume_col: str) -> pd.DataFrame:
        """Generate VWAP (Volume Weighted Average Price) features."""
        vwap_df = pd.DataFrame(index=data.index)
        
        # Calculate typical price
        typical_price = (data['high'] + data['low'] + data['close']) / 3
        
        for window in self.volume_windows:
            try:
                # Calculate VWAP
                vwap = (typical_price * data[volume_col]).rolling(window=window).sum() / data[volume_col].rolling(window=window).sum()
                vwap_df[f"vwap_{window}"] = vwap
                
                # VWAP deviation (price relative to VWAP)
                vwap_deviation = (data['close'] - vwap) / vwap
                vwap_df[f"vwap_deviation_{window}"] = vwap_deviation
                
                # Price above/below VWAP signal
                price_above_vwap = (data['close'] > vwap).astype(int)
                vwap_df[f"price_above_vwap_{window}"] = price_above_vwap
                
            except Exception as e:
                self.logger.warning(f"Error calculating VWAP for window {window}: {str(e)}")
        
        self.logger.debug(f"Generated {len(vwap_df.columns)} VWAP features")
        return vwap_df
    
    def _generate_obv_features(self, data: pd.DataFrame, volume_col: str) -> pd.DataFrame:
        """Generate On-Balance Volume features."""
        obv_df = pd.DataFrame(index=data.index)
        
        try:
            # Calculate OBV
            price_change = data['close'].diff()
            obv = pd.Series(0.0, index=data.index)
            
            for i in range(1, len(data)):
                if price_change.iloc[i] > 0:
                    obv.iloc[i] = obv.iloc[i-1] + data[volume_col].iloc[i]
                elif price_change.iloc[i] < 0:
                    obv.iloc[i] = obv.iloc[i-1] - data[volume_col].iloc[i]
                else:
                    obv.iloc[i] = obv.iloc[i-1]
            
            obv_df['obv'] = obv
            
            # OBV moving averages
            obv_df['obv_sma_10'] = obv.rolling(window=10).mean()
            obv_df['obv_sma_20'] = obv.rolling(window=20).mean()
            
            # OBV momentum
            obv_df['obv_momentum'] = obv.diff(5)
            
        except Exception as e:
            self.logger.warning(f"Error calculating OBV: {str(e)}")
        
        self.logger.debug(f"Generated {len(obv_df.columns)} OBV features")
        return obv_df
    
    def _generate_volume_spike_features(self, data: pd.DataFrame, volume_col: str) -> pd.DataFrame:
        """Generate volume spike detection features."""
        spike_df = pd.DataFrame(index=data.index)
        
        try:
            volume = data[volume_col]
            
            # Volume Z-score (standardized volume)
            volume_mean = volume.rolling(window=20).mean()
            volume_std = volume.rolling(window=20).std()
            volume_z_score = (volume - volume_mean) / volume_std
            spike_df['volume_z_score'] = volume_z_score
            
            # Volume spike detection
            volume_spike = (volume_z_score > self.spike_threshold).astype(int)
            spike_df['volume_spike'] = volume_spike
            
            # Volume percentile
            volume_percentile = volume.rolling(window=100).rank(pct=True)
            spike_df['volume_percentile'] = volume_percentile
            
        except Exception as e:
            self.logger.warning(f"Error calculating volume spikes: {str(e)}")
        
        self.logger.debug(f"Generated {len(spike_df.columns)} volume spike features")
        return spike_df
    
    def _generate_price_volume_correlation_features(self, data: pd.DataFrame, volume_col: str) -> pd.DataFrame:
        """Generate price-volume correlation features."""
        corr_df = pd.DataFrame(index=data.index)
        
        # Calculate price change
        price_change = data['close'].pct_change()
        volume = data[volume_col]
        
        for window in self.pv_windows:
            try:
                # Rolling correlation between price change and volume
                price_volume_corr = price_change.rolling(window=window).corr(volume)
                corr_df[f"price_volume_corr_{window}"] = price_volume_corr
                
                # Volume-price divergence detection
                # High volume with small price change suggests potential reversal
                volume_normalized = (volume - volume.rolling(window=window).mean()) / volume.rolling(window=window).std()
                price_change_abs = np.abs(price_change)
                price_change_normalized = (price_change_abs - price_change_abs.rolling(window=window).mean()) / price_change_abs.rolling(window=window).std()
                
                divergence = volume_normalized - price_change_normalized
                corr_df[f"volume_price_divergence_{window}"] = divergence
                
            except Exception as e:
                self.logger.warning(f"Error calculating price-volume correlation for window {window}: {str(e)}")
        
        self.logger.debug(f"Generated {len(corr_df.columns)} price-volume correlation features")
        return corr_df
    
    def _generate_volume_profile_features(self, data: pd.DataFrame, volume_col: str) -> pd.DataFrame:
        """Generate volume profile and trend features."""
        profile_df = pd.DataFrame(index=data.index)
        
        volume = data[volume_col]
        
        for window in self.volume_windows:
            try:
                # Volume moving average
                volume_sma = volume.rolling(window=window).mean()
                profile_df[f"volume_sma_{window}"] = volume_sma
                
                # Volume ratio (current volume vs average)
                volume_ratio = volume / volume_sma
                profile_df[f"volume_ratio_{window}"] = volume_ratio
                
                # Volume trend (slope of volume over time)
                volume_trend = volume.rolling(window=window).apply(
                    lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == window else np.nan,
                    raw=True
                )
                profile_df[f"volume_trend_{window}"] = volume_trend
                
            except Exception as e:
                self.logger.warning(f"Error calculating volume profile for window {window}: {str(e)}")
        
        self.logger.debug(f"Generated {len(profile_df.columns)} volume profile features")
        return profile_df
