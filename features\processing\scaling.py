"""
Feature Scaling and Normalization Engine

Provides comprehensive feature scaling and normalization capabilities including:
- Multiple scaling methods (Standard, MinMax, Robust, Quantile)
- Adaptive scaling based on feature distributions
- Time-aware scaling for financial time series
- Outlier-robust scaling methods
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Union
from sklearn.preprocessing import (
    StandardScaler, MinMaxScaler, RobustScaler, 
    QuantileTransformer, PowerTransformer
)
from sklearn.base import BaseEstimator, TransformerMixin
import warnings

# Import base classes
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from features.core.base_feature import BaseFeatureEngine, FeatureResult
from features.config.feature_config import FeatureConfig
from data_collection.config import Config


class FeatureScaler(BaseEstimator, TransformerMixin):
    """
    Comprehensive feature scaling engine for financial time series.
    
    Provides multiple scaling methods with automatic method selection
    based on feature characteristics and distribution properties.
    """
    
    def __init__(self, 
                 method: str = 'auto',
                 handle_outliers: bool = True,
                 time_aware: bool = True,
                 preserve_zero: bool = False):
        """
        Initialize feature scaler.
        
        Args:
            method: Scaling method ('auto', 'standard', 'minmax', 'robust', 'quantile', 'power')
            handle_outliers: Whether to use outlier-robust methods
            time_aware: Whether to use time-aware scaling
            preserve_zero: Whether to preserve zero values in scaling
        """
        self.method = method
        self.handle_outliers = handle_outliers
        self.time_aware = time_aware
        self.preserve_zero = preserve_zero
        
        # Fitted scalers for each feature
        self.scalers_ = {}
        self.feature_methods_ = {}
        self.feature_stats_ = {}
        
    def fit(self, X: pd.DataFrame, y: Optional[pd.Series] = None) -> 'FeatureScaler':
        """
        Fit the scaler to the data.
        
        Args:
            X: Input features DataFrame
            y: Target variable (optional)
            
        Returns:
            Fitted scaler instance
        """
        self.scalers_ = {}
        self.feature_methods_ = {}
        self.feature_stats_ = {}
        
        for column in X.columns:
            # Get feature data and handle infinite values
            feature_data = X[column].copy()

            # Replace infinite values with NaN
            feature_data = feature_data.replace([np.inf, -np.inf], np.nan)

            # Drop NaN values
            feature_data = feature_data.dropna()

            if len(feature_data) == 0:
                # If no valid data, skip this feature
                self.logger.warning(f"No valid data for feature {column}, skipping scaling")
                continue

            # Analyze feature characteristics
            stats = self._analyze_feature(feature_data)
            self.feature_stats_[column] = stats

            # Select appropriate scaling method
            if self.method == 'auto':
                scaling_method = self._select_scaling_method(stats)
            else:
                scaling_method = self.method

            self.feature_methods_[column] = scaling_method

            # Fit the selected scaler
            scaler = self._create_scaler(scaling_method)

            # Reshape for sklearn
            feature_values = feature_data.values.reshape(-1, 1)

            # Double-check for any remaining infinite values
            if np.isinf(feature_values).any():
                self.logger.warning(f"Infinite values still present in {column}, replacing with median")
                median_val = np.nanmedian(feature_values)
                if np.isnan(median_val):
                    median_val = 0
                feature_values = np.where(np.isinf(feature_values), median_val, feature_values)

            scaler.fit(feature_values)

            self.scalers_[column] = scaler
            
        return self
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Transform features using fitted scalers.
        
        Args:
            X: Input features DataFrame
            
        Returns:
            Scaled features DataFrame
        """
        if not self.scalers_:
            raise ValueError("Scaler must be fitted before transform")
            
        X_scaled = X.copy()
        
        for column in X.columns:
            if column in self.scalers_:
                scaler = self.scalers_[column]
                
                # Handle missing values and infinite values
                feature_data = X[column].copy()

                # Replace infinite values with NaN
                feature_data = feature_data.replace([np.inf, -np.inf], np.nan)

                mask = feature_data.notna()
                # Ensure we get a scalar count
                try:
                    if hasattr(mask, 'sum'):
                        mask_sum = mask.sum()
                        # If it's a Series, take the first value or sum all values
                        if hasattr(mask_sum, 'iloc'):
                            mask_count = int(mask_sum.iloc[0]) if len(mask_sum) > 0 else 0
                        else:
                            mask_count = int(mask_sum)
                    else:
                        mask_count = len(mask)
                except:
                    mask_count = len(mask) if hasattr(mask, '__len__') else 0

                if mask_count > 0:
                    # Handle both Series and DataFrame cases
                    if hasattr(mask, 'iloc'):
                        # If mask is a DataFrame, get the column for this feature
                        if len(mask.shape) > 1:
                            feature_mask = mask.iloc[:, 0] if len(mask.columns) > 0 else mask.any(axis=1)
                        else:
                            feature_mask = mask
                    else:
                        feature_mask = mask

                    values = feature_data.loc[feature_mask].values.reshape(-1, 1)

                    # Double-check for any remaining infinite values
                    if np.isinf(values).any():
                        median_val = np.nanmedian(values)
                        if np.isnan(median_val):
                            median_val = 0
                        values = np.where(np.isinf(values), median_val, values)

                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        scaled_values = scaler.transform(values)

                    # Store scaled values back to the result
                    # Ensure column name is a string, not a tuple
                    col_name = str(column) if not isinstance(column, str) else column
                    scaled_flat = scaled_values.flatten()

                    try:
                        # Ensure the mask and values have the same length
                        if len(scaled_flat) == feature_mask.sum():
                            X_scaled.loc[feature_mask, col_name] = scaled_flat
                        else:
                            # Fallback: use direct assignment with proper indexing
                            valid_indices = X_scaled.index[feature_mask]
                            if len(valid_indices) == len(scaled_flat):
                                X_scaled.loc[valid_indices, col_name] = scaled_flat
                            else:
                                # Last resort: fill with median value
                                median_val = np.nanmedian(scaled_flat) if len(scaled_flat) > 0 else 0.0
                                X_scaled.loc[feature_mask, col_name] = median_val
                    except (KeyError, ValueError) as e:
                        # Fallback: use integer indexing
                        try:
                            col_idx = X_scaled.columns.get_loc(col_name) if col_name in X_scaled.columns else 0
                            X_scaled.iloc[feature_mask, col_idx] = scaled_flat
                        except:
                            # Final fallback: fill with median
                            median_val = np.nanmedian(scaled_flat) if len(scaled_flat) > 0 else 0.0
                            X_scaled.iloc[:, col_idx] = median_val
                    
        return X_scaled
    
    def fit_transform(self, X: pd.DataFrame, y: Optional[pd.Series] = None) -> pd.DataFrame:
        """Fit and transform in one step."""
        return self.fit(X, y).transform(X)
    
    def _analyze_feature(self, data: pd.Series) -> Dict[str, float]:
        """Analyze feature characteristics for scaling method selection."""
        stats = {
            'mean': data.mean(),
            'std': data.std(),
            'min': data.min(),
            'max': data.max(),
            'skewness': data.skew(),
            'kurtosis': data.kurtosis(),
            'zero_ratio': (data == 0).mean(),
            'outlier_ratio': self._calculate_outlier_ratio(data),
            'range_ratio': (data.max() - data.min()) / (data.std() + 1e-8)
        }
        return stats
    
    def _calculate_outlier_ratio(self, data: pd.Series) -> float:
        """Calculate the ratio of outliers using IQR method."""
        Q1 = data.quantile(0.25)
        Q3 = data.quantile(0.75)
        IQR = Q3 - Q1
        
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = (data < lower_bound) | (data > upper_bound)
        return outliers.mean()
    
    def _select_scaling_method(self, stats: Dict[str, float]) -> str:
        """Select appropriate scaling method based on feature statistics."""
        # Convert Series to scalar values by taking the mean
        outlier_ratio = float(stats['outlier_ratio'].mean()) if hasattr(stats['outlier_ratio'], 'mean') else float(stats['outlier_ratio'])
        skewness = float(abs(stats['skewness']).mean()) if hasattr(stats['skewness'], 'mean') else float(abs(stats['skewness']))
        zero_ratio = float(stats['zero_ratio'].mean()) if hasattr(stats['zero_ratio'], 'mean') else float(stats['zero_ratio'])
        kurtosis = float(stats['kurtosis'].mean()) if hasattr(stats['kurtosis'], 'mean') else float(stats['kurtosis'])

        # High outlier ratio -> use robust scaling
        if outlier_ratio > 0.1:
            return 'robust'

        # High skewness -> use power transformation
        if skewness > 2.0:
            return 'power'

        # Binary or sparse features -> use minmax
        if zero_ratio > 0.8:
            return 'minmax'

        # Heavy tails -> use quantile transformation
        if kurtosis > 5.0:
            return 'quantile'

        # Default to standard scaling
        return 'standard'
    
    def _create_scaler(self, method: str) -> BaseEstimator:
        """Create scaler instance based on method."""
        if method == 'standard':
            return StandardScaler()
        elif method == 'minmax':
            return MinMaxScaler()
        elif method == 'robust':
            return RobustScaler()
        elif method == 'quantile':
            return QuantileTransformer(output_distribution='normal', random_state=42)
        elif method == 'power':
            return PowerTransformer(method='yeo-johnson', standardize=True)
        else:
            raise ValueError(f"Unknown scaling method: {method}")
    
    def get_feature_info(self) -> pd.DataFrame:
        """Get information about scaling methods used for each feature."""
        if not self.feature_methods_:
            return pd.DataFrame()
            
        info_data = []
        for feature, method in self.feature_methods_.items():
            stats = self.feature_stats_.get(feature, {})
            info_data.append({
                'feature': feature,
                'scaling_method': method,
                'outlier_ratio': stats.get('outlier_ratio', 0),
                'skewness': stats.get('skewness', 0),
                'kurtosis': stats.get('kurtosis', 0),
                'zero_ratio': stats.get('zero_ratio', 0)
            })
            
        return pd.DataFrame(info_data)


class FeatureScalingEngine(BaseFeatureEngine):
    """
    Feature scaling engine that integrates with the feature engineering pipeline.
    
    Provides comprehensive scaling capabilities for all generated features
    with automatic method selection and quality assessment.
    """
    
    def __init__(self, config: Config, feature_config: FeatureConfig, **kwargs):
        """
        Initialize feature scaling engine.
        
        Args:
            config: Main system configuration
            feature_config: Feature-specific configuration
            **kwargs: Additional parameters
        """
        super().__init__(config)
        self.feature_config = feature_config
        self.scaling_config = feature_config.processing.get('scaling', {})
        
        # Scaling configuration
        self.default_method = self.scaling_config.get('default_method', 'auto')
        self.handle_outliers = self.scaling_config.get('handle_outliers', True)
        self.time_aware = self.scaling_config.get('time_aware', True)
        
        # Feature-specific scaling methods
        self.feature_methods = self.scaling_config.get('feature_methods', {})
        
        # Fitted scaler
        self.scaler_ = None
        
    def get_feature_type(self) -> str:
        """Get the type of processing this engine performs."""
        return "feature_scaling"
    
    def get_required_columns(self) -> List[str]:
        """Get required columns (accepts any numerical columns)."""
        return []  # Can work with any numerical features
    
    def get_feature_names(self) -> List[str]:
        """Get list of feature names (same as input for scaling)."""
        return []  # Will be determined at runtime
    
    def fit(self, data: pd.DataFrame, **kwargs) -> 'FeatureScalingEngine':
        """
        Fit the scaling engine to the data.
        
        Args:
            data: Input features DataFrame
            **kwargs: Additional parameters
            
        Returns:
            Fitted scaling engine
        """
        # Ensure datetime index
        data = self._ensure_datetime_index(data)
        
        # Select numerical columns only
        numerical_cols = data.select_dtypes(include=[np.number]).columns
        numerical_data = data[numerical_cols]
        
        # Create and fit scaler
        self.scaler_ = FeatureScaler(
            method=self.default_method,
            handle_outliers=self.handle_outliers,
            time_aware=self.time_aware
        )
        
        self.scaler_.fit(numerical_data)
        
        self.logger.info(f"Fitted scaler for {len(numerical_cols)} numerical features")
        return self
    
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Transform features using fitted scaler.
        
        Args:
            data: Input features DataFrame
            **kwargs: Additional parameters
            
        Returns:
            Scaled features DataFrame
        """
        if self.scaler_ is None:
            raise ValueError("Scaler must be fitted before transform")
            
        # Ensure datetime index
        data = self._ensure_datetime_index(data)
        
        # Select numerical columns only
        numerical_cols = data.select_dtypes(include=[np.number]).columns
        
        # Transform numerical features
        scaled_data = data.copy()
        if len(numerical_cols) > 0:
            numerical_data = data[numerical_cols]
            scaled_numerical = self.scaler_.transform(numerical_data)
            scaled_data[numerical_cols] = scaled_numerical
        
        self.logger.info(f"Scaled {len(numerical_cols)} numerical features")
        return scaled_data
    
    def fit_transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Fit and transform in one step."""
        return self.fit(data, **kwargs).transform(data, **kwargs)
    
    def _generate_features_impl(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Implementation for feature generation interface.
        For scaling, this performs fit_transform.
        """
        return self.fit_transform(data, **kwargs)
    
    def get_scaling_info(self) -> pd.DataFrame:
        """Get information about scaling methods used."""
        if self.scaler_ is None:
            return pd.DataFrame()
        return self.scaler_.get_feature_info()
    
    def save_scaler(self, filepath: str):
        """Save fitted scaler to file."""
        import joblib
        if self.scaler_ is not None:
            joblib.dump(self.scaler_, filepath)
            self.logger.info(f"Saved scaler to {filepath}")
    
    def load_scaler(self, filepath: str):
        """Load fitted scaler from file."""
        import joblib
        self.scaler_ = joblib.load(filepath)
        self.logger.info(f"Loaded scaler from {filepath}")
