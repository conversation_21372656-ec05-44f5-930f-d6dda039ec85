"""
Feature Selection and Redundancy Elimination Engine

Provides comprehensive feature selection capabilities including:
- Correlation-based redundancy elimination
- Variance-based feature filtering
- Mutual information feature selection
- Statistical significance testing
- Model-based feature importance
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Union
from sklearn.feature_selection import (
    VarianceThreshold, SelectKBest, SelectPercentile,
    mutual_info_regression, f_regression
)
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LassoCV
import warnings

# Import base classes
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from features.core.base_feature import BaseFeatureEngine, FeatureResult
from features.config.feature_config import FeatureConfig
from data_collection.config import Config


class FeatureSelector:
    """
    Comprehensive feature selection engine for financial time series.
    
    Combines multiple selection methods to identify the most informative
    features while eliminating redundancy and noise.
    """
    
    def __init__(self,
                 correlation_threshold: float = 0.95,
                 variance_threshold: float = 0.01,
                 max_features: Optional[int] = None,
                 selection_methods: List[str] = None):
        """
        Initialize feature selector.
        
        Args:
            correlation_threshold: Threshold for correlation-based redundancy elimination
            variance_threshold: Minimum variance threshold for features
            max_features: Maximum number of features to select
            selection_methods: List of selection methods to use
        """
        self.correlation_threshold = correlation_threshold
        self.variance_threshold = variance_threshold
        self.max_features = max_features
        
        if selection_methods is None:
            selection_methods = ['variance', 'correlation', 'mutual_info']
        self.selection_methods = selection_methods
        
        # Fitted selectors and results
        self.selected_features_ = None
        self.feature_scores_ = {}
        self.elimination_reasons_ = {}
        
    def fit_select(self, X: pd.DataFrame, y: Optional[pd.Series] = None) -> pd.DataFrame:
        """
        Fit selector and return selected features.
        
        Args:
            X: Input features DataFrame
            y: Target variable (optional, required for supervised methods)
            
        Returns:
            Selected features DataFrame
        """
        self.selected_features_ = list(X.columns)
        self.feature_scores_ = {}
        self.elimination_reasons_ = {}
        
        # Apply selection methods in sequence
        for method in self.selection_methods:
            if method == 'variance':
                self._apply_variance_selection(X)
            elif method == 'correlation':
                self._apply_correlation_selection(X)
            elif method == 'mutual_info' and y is not None:
                self._apply_mutual_info_selection(X, y)
            elif method == 'statistical' and y is not None:
                self._apply_statistical_selection(X, y)
            elif method == 'model_based' and y is not None:
                self._apply_model_based_selection(X, y)
        
        # Apply max_features constraint
        if self.max_features and len(self.selected_features_) > self.max_features:
            self._apply_max_features_constraint(X, y)
        
        return X[self.selected_features_]
    
    def _apply_variance_selection(self, X: pd.DataFrame):
        """Apply variance-based feature selection."""
        # Get numerical features only
        numerical_features = X.select_dtypes(include=[np.number]).columns
        if len(numerical_features) == 0:
            return

        try:
            # Handle infinite and NaN values
            X_numerical = X[numerical_features].copy()

            # Replace infinite values with NaN, then fill with median
            X_numerical = X_numerical.replace([np.inf, -np.inf], np.nan)

            # Fill NaN values with column medians (more robust than 0)
            for col in X_numerical.columns:
                if X_numerical[col].isna().all():
                    # If all values are NaN, fill with 0
                    X_numerical[col] = 0
                else:
                    # Fill with median of non-NaN values
                    median_val = X_numerical[col].median()
                    if pd.isna(median_val):
                        median_val = 0
                    X_numerical[col] = X_numerical[col].fillna(median_val)

            # Calculate variances manually to handle edge cases
            variances = X_numerical.var()

            # Identify low variance features
            eliminated_features = []
            for feature in numerical_features:
                if feature in variances.index:
                    var_val = variances[feature]
                    # Ensure var_val is a scalar, not a Series
                    if hasattr(var_val, 'iloc'):
                        var_val = var_val.iloc[0] if len(var_val) > 0 else 0

                    if pd.isna(var_val) or (isinstance(var_val, (int, float)) and var_val <= self.variance_threshold):
                        eliminated_features.append(feature)
                        self.elimination_reasons_[feature] = 'low_variance'

            # Update selected features
            self.selected_features_ = [f for f in self.selected_features_
                                     if f not in eliminated_features]

            # Store variance scores (only for valid variances)
            valid_variances = {k: v for k, v in variances.to_dict().items()
                             if not pd.isna(v)}
            self.feature_scores_['variance'] = valid_variances

        except Exception as e:
            warnings.warn(f"Variance selection failed: {str(e)}")
            # Don't eliminate any features if variance selection fails
            pass
    
    def _apply_correlation_selection(self, X: pd.DataFrame):
        """Apply correlation-based redundancy elimination."""
        if len(self.selected_features_) <= 1:
            return

        # Calculate correlation matrix for selected features
        selected_data = X[self.selected_features_].select_dtypes(include=[np.number])

        if selected_data.empty:
            return

        try:
            # Handle infinite and NaN values before correlation calculation
            selected_data_clean = selected_data.copy()

            # Replace infinite values with NaN
            selected_data_clean = selected_data_clean.replace([np.inf, -np.inf], np.nan)

            # Fill NaN values with column medians
            for col in selected_data_clean.columns:
                if selected_data_clean[col].isna().all():
                    selected_data_clean[col] = 0
                else:
                    median_val = selected_data_clean[col].median()
                    if pd.isna(median_val):
                        median_val = 0
                    selected_data_clean[col] = selected_data_clean[col].fillna(median_val)

            # Calculate correlation matrix
            corr_matrix = selected_data_clean.corr().abs()

            # Handle NaN correlations (replace with 0)
            corr_matrix = corr_matrix.fillna(0)

            # Find highly correlated feature pairs
            eliminated_features = set()

            for i in range(len(corr_matrix.columns)):
                for j in range(i + 1, len(corr_matrix.columns)):
                    feature1 = corr_matrix.columns[i]
                    feature2 = corr_matrix.columns[j]

                    corr_val = corr_matrix.iloc[i, j]

                    # Check if correlation is valid and above threshold
                    if (not pd.isna(corr_val) and
                        corr_val > self.correlation_threshold and
                        feature1 not in eliminated_features and
                        feature2 not in eliminated_features):

                        # Eliminate the feature with lower variance
                        var1 = selected_data_clean[feature1].var()
                        var2 = selected_data_clean[feature2].var()

                        # Handle NaN variances
                        if pd.isna(var1):
                            var1 = 0
                        if pd.isna(var2):
                            var2 = 0

                        if var1 < var2:
                            eliminated_features.add(feature1)
                            self.elimination_reasons_[feature1] = f'correlated_with_{feature2}'
                        else:
                            eliminated_features.add(feature2)
                            self.elimination_reasons_[feature2] = f'correlated_with_{feature1}'

            # Update selected features
            self.selected_features_ = [f for f in self.selected_features_
                                     if f not in eliminated_features]

            # Store correlation scores (max correlation with other features)
            max_correlations = {}
            for feature in corr_matrix.columns:
                if feature in self.selected_features_:
                    other_corrs = corr_matrix[feature].drop(feature)
                    # Use .max() with skipna=True to handle NaN values
                    max_corr = other_corrs.max(skipna=True) if len(other_corrs) > 0 else 0
                    if pd.isna(max_corr):
                        max_corr = 0
                    max_correlations[feature] = max_corr

            self.feature_scores_['max_correlation'] = max_correlations

        except Exception as e:
            warnings.warn(f"Correlation selection failed: {str(e)}")
            # Don't eliminate any features if correlation selection fails
            pass
    
    def _apply_mutual_info_selection(self, X: pd.DataFrame, y: pd.Series):
        """Apply mutual information-based feature selection."""
        if len(self.selected_features_) == 0:
            return
            
        selected_data = X[self.selected_features_].select_dtypes(include=[np.number])
        
        if selected_data.empty:
            return
            
        try:
            # Align data and target
            common_index = selected_data.index.intersection(y.index)
            X_aligned = selected_data.loc[common_index].fillna(0)
            y_aligned = y.loc[common_index]
            
            if len(X_aligned) == 0:
                return
            
            # Calculate mutual information scores
            mi_scores = mutual_info_regression(X_aligned, y_aligned, random_state=42)
            
            # Store scores
            mi_dict = dict(zip(X_aligned.columns, mi_scores))
            self.feature_scores_['mutual_info'] = mi_dict
            
            # Select top features based on mutual information
            if self.max_features:
                n_select = min(self.max_features, len(mi_scores))
                top_indices = np.argsort(mi_scores)[-n_select:]
                top_features = X_aligned.columns[top_indices].tolist()
                
                eliminated_features = [f for f in self.selected_features_ 
                                     if f not in top_features and f in X_aligned.columns]
                
                for feature in eliminated_features:
                    self.elimination_reasons_[feature] = 'low_mutual_info'
                
                # Keep non-numerical features
                non_numerical = [f for f in self.selected_features_ 
                               if f not in X_aligned.columns]
                self.selected_features_ = top_features + non_numerical
            
        except Exception as e:
            warnings.warn(f"Mutual information selection failed: {str(e)}")
    
    def _apply_statistical_selection(self, X: pd.DataFrame, y: pd.Series):
        """Apply statistical significance-based feature selection."""
        if len(self.selected_features_) == 0:
            return
            
        selected_data = X[self.selected_features_].select_dtypes(include=[np.number])
        
        if selected_data.empty:
            return
            
        try:
            # Align data and target
            common_index = selected_data.index.intersection(y.index)
            X_aligned = selected_data.loc[common_index].fillna(0)
            y_aligned = y.loc[common_index]
            
            if len(X_aligned) == 0:
                return
            
            # Calculate F-statistics
            f_scores, p_values = f_regression(X_aligned, y_aligned)
            
            # Store scores
            f_dict = dict(zip(X_aligned.columns, f_scores))
            p_dict = dict(zip(X_aligned.columns, p_values))
            
            self.feature_scores_['f_statistic'] = f_dict
            self.feature_scores_['p_value'] = p_dict
            
        except Exception as e:
            warnings.warn(f"Statistical selection failed: {str(e)}")
    
    def _apply_model_based_selection(self, X: pd.DataFrame, y: pd.Series):
        """Apply model-based feature importance selection."""
        if len(self.selected_features_) == 0:
            return
            
        selected_data = X[self.selected_features_].select_dtypes(include=[np.number])
        
        if selected_data.empty:
            return
            
        try:
            # Align data and target
            common_index = selected_data.index.intersection(y.index)
            X_aligned = selected_data.loc[common_index].fillna(0)
            y_aligned = y.loc[common_index]
            
            if len(X_aligned) == 0 or len(X_aligned.columns) == 0:
                return
            
            # Random Forest feature importance
            rf = RandomForestRegressor(n_estimators=50, random_state=42, n_jobs=-1)
            rf.fit(X_aligned, y_aligned)
            
            rf_importance = dict(zip(X_aligned.columns, rf.feature_importances_))
            self.feature_scores_['rf_importance'] = rf_importance
            
            # LASSO feature selection
            lasso = LassoCV(cv=3, random_state=42, max_iter=1000)
            lasso.fit(X_aligned, y_aligned)
            
            lasso_coef = dict(zip(X_aligned.columns, np.abs(lasso.coef_)))
            self.feature_scores_['lasso_coef'] = lasso_coef
            
        except Exception as e:
            warnings.warn(f"Model-based selection failed: {str(e)}")
    
    def _apply_max_features_constraint(self, X: pd.DataFrame, y: Optional[pd.Series]):
        """Apply maximum features constraint using combined scores."""
        if len(self.selected_features_) <= self.max_features:
            return
            
        # Combine scores to rank features
        feature_rankings = self._calculate_combined_rankings()
        
        # Select top features
        top_features = list(feature_rankings.head(self.max_features).index)
        
        eliminated_features = [f for f in self.selected_features_ if f not in top_features]
        for feature in eliminated_features:
            self.elimination_reasons_[feature] = 'max_features_constraint'
        
        self.selected_features_ = top_features
    
    def _calculate_combined_rankings(self) -> pd.Series:
        """Calculate combined feature rankings from all scores."""
        all_features = set()
        for scores in self.feature_scores_.values():
            all_features.update(scores.keys())
        
        combined_scores = pd.DataFrame(index=list(all_features))
        
        # Normalize and combine scores
        for score_name, scores in self.feature_scores_.items():
            if score_name in ['p_value']:  # Lower is better
                normalized = 1 - pd.Series(scores).rank(pct=True)
            else:  # Higher is better
                normalized = pd.Series(scores).rank(pct=True)
            
            combined_scores[score_name] = normalized
        
        # Calculate mean ranking
        combined_scores['mean_rank'] = combined_scores.mean(axis=1)
        
        return combined_scores['mean_rank'].sort_values(ascending=False)
    
    def get_selection_summary(self) -> Dict[str, Any]:
        """Get summary of feature selection process."""
        return {
            'total_features_input': len(self.selected_features_) + len(self.elimination_reasons_),
            'selected_features': len(self.selected_features_),
            'eliminated_features': len(self.elimination_reasons_),
            'elimination_reasons': dict(pd.Series(self.elimination_reasons_).value_counts()),
            'feature_scores_available': list(self.feature_scores_.keys())
        }


class FeatureSelectionEngine(BaseFeatureEngine):
    """
    Feature selection engine that integrates with the feature engineering pipeline.
    
    Provides comprehensive feature selection and redundancy elimination
    for optimizing the feature set for machine learning models.
    """
    
    def __init__(self, config: Config, feature_config: FeatureConfig, **kwargs):
        """
        Initialize feature selection engine.
        
        Args:
            config: Main system configuration
            feature_config: Feature-specific configuration
            **kwargs: Additional parameters
        """
        super().__init__(config)
        self.feature_config = feature_config
        self.selection_config = feature_config.processing.get('selection', {})
        
        # Selection configuration
        self.correlation_threshold = self.selection_config.get('correlation_threshold', 0.95)
        self.variance_threshold = self.selection_config.get('variance_threshold', 0.01)
        self.max_features = self.selection_config.get('max_features', None)
        self.selection_methods = self.selection_config.get('methods', 
                                                          ['variance', 'correlation'])
        
        # Fitted selector
        self.selector_ = None
        
    def get_feature_type(self) -> str:
        """Get the type of processing this engine performs."""
        return "feature_selection"
    
    def get_required_columns(self) -> List[str]:
        """Get required columns (accepts any columns)."""
        return []  # Can work with any features
    
    def get_feature_names(self) -> List[str]:
        """Get list of feature names (determined at runtime)."""
        return []  # Will be determined after selection
    
    def select_features(self, data: pd.DataFrame, target: Optional[pd.Series] = None, **kwargs) -> pd.DataFrame:
        """
        Select features from input data.
        
        Args:
            data: Input features DataFrame
            target: Target variable for supervised selection
            **kwargs: Additional parameters
            
        Returns:
            Selected features DataFrame
        """
        # Ensure datetime index
        data = self._ensure_datetime_index(data)
        
        # Create and apply selector
        self.selector_ = FeatureSelector(
            correlation_threshold=self.correlation_threshold,
            variance_threshold=self.variance_threshold,
            max_features=self.max_features,
            selection_methods=self.selection_methods
        )
        
        selected_data = self.selector_.fit_select(data, target)
        
        self.logger.info(f"Selected {len(selected_data.columns)} features from {len(data.columns)} input features")
        
        return selected_data
    
    def _generate_features_impl(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Implementation for feature generation interface.
        For selection, this performs feature selection.
        """
        target = kwargs.get('target', None)
        return self.select_features(data, target, **kwargs)
    
    def get_selection_summary(self) -> Dict[str, Any]:
        """Get summary of feature selection process."""
        if self.selector_ is None:
            return {}
        return self.selector_.get_selection_summary()
    
    def get_selected_features(self) -> List[str]:
        """Get list of selected feature names."""
        if self.selector_ is None:
            return []
        return self.selector_.selected_features_
    
    def get_feature_scores(self) -> Dict[str, Dict[str, float]]:
        """Get feature scores from selection process."""
        if self.selector_ is None:
            return {}
        return self.selector_.feature_scores_
