"""
Feature Processing Module

Provides feature processing utilities including:
- Multiple scaling methods
- Feature selection algorithms
- Feature optimization
"""

from .scaling import FeatureScaler, FeatureScalingEngine
from .selection import FeatureSelector, FeatureSelectionEngine
from .pipeline import FeatureProcessingPipeline, FeatureProcessingEngine

__all__ = [
    'FeatureScaler',
    'FeatureScalingEngine',
    'FeatureSelector',
    'FeatureSelectionEngine',
    'FeatureProcessingPipeline',
    'FeatureProcessingEngine'
]

__all__ = [
    'FeatureScaler',
    'FeatureSelector',
    'FeatureOptimizer'
]
