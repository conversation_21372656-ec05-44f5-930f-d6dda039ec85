"""
XAUUSD Feature Dataset Generation

Generates a comprehensive feature dataset for reinforcement learning model training.
This script executes the complete feature engineering pipeline on historical XAUUSD data.
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from features.pipeline.production import ProductionFeaturePipeline
from features.validation.quality import FeatureQualityValidator
from data_collection import Config


def load_xauusd_data() -> pd.DataFrame:
    """Load and combine XAUUSD data from multiple timeframes."""
    print("Loading XAUUSD historical data...")
    
    # Load 5-minute data as primary timeframe
    data_path = Path("data/mt5/XAUUSD_5m.csv")
    
    if not data_path.exists():
        raise FileNotFoundError(f"XAUUSD data not found at {data_path}")
    
    # Load primary data
    data = pd.read_csv(data_path)
    data['datetime'] = pd.to_datetime(data['datetime'])
    data = data.set_index('datetime')

    # Standardize column names for our feature engineering system
    if 'tick_volume' in data.columns:
        data['volume'] = data['tick_volume']
    elif 'real_volume' in data.columns and data['real_volume'].sum() > 0:
        data['volume'] = data['real_volume']
    else:
        # If no volume data, create synthetic volume based on price movement
        data['volume'] = abs(data['close'].pct_change()) * 1000000

    # Keep only OHLCV columns for feature engineering
    required_columns = ['open', 'high', 'low', 'close', 'volume']
    data = data[required_columns]

    # Sort by datetime to ensure proper order
    data = data.sort_index()

    # Remove any duplicate timestamps
    data = data[~data.index.duplicated(keep='first')]
    
    print(f"✓ Loaded XAUUSD 5m data: {len(data)} records")
    print(f"  Date range: {data.index[0]} to {data.index[-1]}")
    print(f"  Columns: {list(data.columns)}")
    
    return data


def create_target_variables(data: pd.DataFrame) -> pd.DataFrame:
    """Create target variables for reinforcement learning."""
    print("Creating target variables...")
    
    targets = pd.DataFrame(index=data.index)
    
    # 1. Price direction (next period)
    targets['price_direction'] = np.sign(data['close'].pct_change().shift(-1))
    
    # 2. Price return (next period)
    targets['price_return'] = data['close'].pct_change().shift(-1)
    
    # 3. Volatility-adjusted return
    volatility = data['close'].pct_change().rolling(20).std()
    targets['vol_adjusted_return'] = targets['price_return'] / volatility
    
    # 4. Multi-period returns (for different holding periods)
    for periods in [5, 15, 30]:  # 25min, 75min, 150min ahead
        targets[f'return_{periods}p'] = data['close'].pct_change(periods).shift(-periods)
        targets[f'direction_{periods}p'] = np.sign(targets[f'return_{periods}p'])
    
    # 5. High-low range (next period)
    targets['next_hl_range'] = ((data['high'] - data['low']) / data['close']).shift(-1)
    
    # 6. Volume-weighted return
    volume_ma = data['volume'].rolling(20).mean()
    volume_weight = data['volume'] / volume_ma
    targets['volume_weighted_return'] = targets['price_return'] * volume_weight.shift(-1)
    
    # Clean up infinite and NaN values
    targets = targets.replace([np.inf, -np.inf], np.nan)
    
    print(f"✓ Created {len(targets.columns)} target variables")
    print(f"  Target variables: {list(targets.columns)}")
    
    return targets


def generate_complete_dataset():
    """Generate the complete feature dataset."""
    print("XAUUSD FEATURE DATASET GENERATION")
    print("=" * 60)
    
    start_time = datetime.now()
    
    try:
        # Step 1: Load data
        data = load_xauusd_data()
        
        # Step 2: Create target variables
        targets = create_target_variables(data)
        
        # Step 3: Split data for training/validation/testing
        print("\nSplitting data for training/validation/testing...")
        
        # Use 70% for training, 15% for validation, 15% for testing
        n_total = len(data)
        n_train = int(n_total * 0.70)
        n_val = int(n_total * 0.15)
        
        train_data = data.iloc[:n_train]
        val_data = data.iloc[n_train:n_train + n_val]
        test_data = data.iloc[n_train + n_val:]
        
        train_targets = targets.iloc[:n_train]
        val_targets = targets.iloc[n_train:n_train + n_val]
        test_targets = targets.iloc[n_train + n_val:]
        
        print(f"  Training set: {len(train_data)} records ({train_data.index[0]} to {train_data.index[-1]})")
        print(f"  Validation set: {len(val_data)} records ({val_data.index[0]} to {val_data.index[-1]})")
        print(f"  Test set: {len(test_data)} records ({test_data.index[0]} to {test_data.index[-1]})")
        
        # Step 4: Create and fit production pipeline
        print("\nCreating production feature pipeline...")
        pipeline = ProductionFeaturePipeline()
        
        print("Fitting pipeline on training data (this may take several minutes)...")
        fit_start = datetime.now()
        
        # Use price direction as primary target for supervised feature selection
        primary_target = train_targets['price_direction'].dropna()
        
        # Align training data with target
        aligned_train_data = train_data.loc[primary_target.index]
        
        pipeline.fit(aligned_train_data, primary_target)
        
        fit_time = (datetime.now() - fit_start).total_seconds()
        print(f"✓ Pipeline fitted successfully in {fit_time:.1f} seconds")
        
        # Step 5: Generate features for all datasets
        print("\nGenerating features for all datasets...")
        
        # Training features
        print("  Generating training features...")
        train_features = pipeline.generate_features(train_data)
        print(f"    Training features: {train_features.shape}")
        
        # Validation features
        print("  Generating validation features...")
        val_features = pipeline.generate_features(val_data)
        print(f"    Validation features: {val_features.shape}")
        
        # Test features
        print("  Generating test features...")
        test_features = pipeline.generate_features(test_data)
        print(f"    Test features: {test_features.shape}")
        
        # Step 6: Quality validation
        print("\nValidating feature quality...")
        validator = FeatureQualityValidator()
        
        # Validate training features
        quality_report = validator.validate_features(train_features, primary_target)
        quality_score = quality_report['overall_quality_score']
        
        print(f"✓ Feature quality validation completed")
        print(f"  Overall quality score: {quality_score:.3f}")
        print(f"  Features analyzed: {quality_report['feature_count']}")
        print(f"  Recommendations: {len(quality_report['recommendations'])}")
        
        # Step 7: Save datasets
        print("\nSaving feature datasets...")
        
        # Create output directory
        output_dir = Path("data/features")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save features
        train_features.to_csv(output_dir / "train_features.csv")
        val_features.to_csv(output_dir / "val_features.csv")
        test_features.to_csv(output_dir / "test_features.csv")
        
        # Save targets
        train_targets.to_csv(output_dir / "train_targets.csv")
        val_targets.to_csv(output_dir / "val_targets.csv")
        test_targets.to_csv(output_dir / "test_targets.csv")
        
        # Save raw data for reference
        train_data.to_csv(output_dir / "train_raw.csv")
        val_data.to_csv(output_dir / "val_raw.csv")
        test_data.to_csv(output_dir / "test_raw.csv")
        
        print(f"✓ Datasets saved to {output_dir}")
        
        # Step 8: Save fitted pipeline
        print("\nSaving fitted pipeline...")
        pipeline_path = Path("data/models/xauusd_feature_pipeline.joblib")
        pipeline_path.parent.mkdir(parents=True, exist_ok=True)
        pipeline.save_pipeline(str(pipeline_path))
        print(f"✓ Pipeline saved to {pipeline_path}")
        
        # Step 9: Generate dataset summary
        print("\nGenerating dataset summary...")
        
        summary = {
            'generation_date': datetime.now().isoformat(),
            'total_processing_time': (datetime.now() - start_time).total_seconds(),
            'data_info': {
                'total_records': len(data),
                'date_range': f"{data.index[0]} to {data.index[-1]}",
                'timeframe': '5 minutes'
            },
            'splits': {
                'train': {'records': len(train_data), 'percentage': 70},
                'validation': {'records': len(val_data), 'percentage': 15},
                'test': {'records': len(test_data), 'percentage': 15}
            },
            'features': {
                'total_features': len(train_features.columns),
                'feature_names': list(train_features.columns),
                'quality_score': quality_score
            },
            'targets': {
                'total_targets': len(train_targets.columns),
                'target_names': list(train_targets.columns)
            },
            'files_generated': [
                'train_features.csv', 'val_features.csv', 'test_features.csv',
                'train_targets.csv', 'val_targets.csv', 'test_targets.csv',
                'train_raw.csv', 'val_raw.csv', 'test_raw.csv'
            ]
        }
        
        # Save summary
        import json
        with open(output_dir / "dataset_summary.json", 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        # Step 10: Generate usage documentation
        print("\nGenerating usage documentation...")
        
        usage_doc = f"""
# XAUUSD Feature Dataset - Usage Guide

## Dataset Overview
- **Generation Date**: {summary['generation_date']}
- **Total Records**: {summary['data_info']['total_records']:,}
- **Date Range**: {summary['data_info']['date_range']}
- **Timeframe**: {summary['data_info']['timeframe']}
- **Total Features**: {summary['features']['total_features']}
- **Quality Score**: {summary['features']['quality_score']:.3f}

## Dataset Splits
- **Training**: {summary['splits']['train']['records']:,} records ({summary['splits']['train']['percentage']}%)
- **Validation**: {summary['splits']['validation']['records']:,} records ({summary['splits']['validation']['percentage']}%)
- **Test**: {summary['splits']['test']['records']:,} records ({summary['splits']['test']['percentage']}%)

## Files Generated
### Feature Files
- `train_features.csv` - Training features
- `val_features.csv` - Validation features  
- `test_features.csv` - Test features

### Target Files
- `train_targets.csv` - Training targets
- `val_targets.csv` - Validation targets
- `test_targets.csv` - Test targets

### Raw Data Files
- `train_raw.csv` - Raw OHLCV training data
- `val_raw.csv` - Raw OHLCV validation data
- `test_raw.csv` - Raw OHLCV test data

### Model Files
- `../models/xauusd_feature_pipeline.joblib` - Fitted feature pipeline

## Usage Example

```python
import pandas as pd
from pathlib import Path

# Load feature datasets
data_dir = Path("data/features")

train_features = pd.read_csv(data_dir / "train_features.csv", index_col=0)
train_targets = pd.read_csv(data_dir / "train_targets.csv", index_col=0)

val_features = pd.read_csv(data_dir / "val_features.csv", index_col=0)
val_targets = pd.read_csv(data_dir / "val_targets.csv", index_col=0)

# Load fitted pipeline for new data processing
from features.pipeline.production import ProductionFeaturePipeline

pipeline = ProductionFeaturePipeline()
pipeline.load_pipeline("data/models/xauusd_feature_pipeline.joblib")

# Generate features for new data
new_features = pipeline.generate_features(new_ohlcv_data)
```

## Feature Categories
The dataset includes {summary['features']['total_features']} features across multiple categories:
- Multi-timeframe price analysis
- Technical indicators (RSI, MACD, Bollinger Bands, etc.)
- Volatility analysis (ATR, volatility expansion, etc.)
- Volume analysis (VWAP, OBV, volume trends, etc.)
- Cross-asset correlations (DXY, SPY, TLT, VIX, QQQ, IEF)
- Market regime detection
- Trading session analysis
- Temporal patterns

## Target Variables
The dataset includes {summary['targets']['total_targets']} target variables:
- Price direction (next period)
- Price returns (multiple horizons)
- Volatility-adjusted returns
- Volume-weighted returns
- High-low range predictions

## Ready for Reinforcement Learning
This dataset is optimized for reinforcement learning model training with:
- Proper temporal splits (no data leakage)
- Comprehensive feature engineering
- Multiple target variables for different strategies
- Quality validation and monitoring
- Production-ready feature pipeline
"""
        
        with open(output_dir / "README.md", 'w') as f:
            f.write(usage_doc)
        
        # Final summary
        total_time = (datetime.now() - start_time).total_seconds()
        
        print("\n" + "=" * 60)
        print("🎉 FEATURE DATASET GENERATION COMPLETED!")
        print("=" * 60)
        
        print(f"✅ Total Processing Time: {total_time:.1f} seconds")
        print(f"✅ Features Generated: {summary['features']['total_features']}")
        print(f"✅ Quality Score: {summary['features']['quality_score']:.3f}")
        print(f"✅ Training Records: {summary['splits']['train']['records']:,}")
        print(f"✅ Validation Records: {summary['splits']['validation']['records']:,}")
        print(f"✅ Test Records: {summary['splits']['test']['records']:,}")
        
        print(f"\n📁 Dataset Location: {output_dir}")
        print(f"📋 Documentation: {output_dir / 'README.md'}")
        print(f"🔧 Pipeline: {pipeline_path}")
        
        print("\n🚀 DATASET READY FOR REINFORCEMENT LEARNING MODEL TRAINING!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ DATASET GENERATION FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = generate_complete_dataset()
    
    if success:
        print("\n" + "🎯" * 20)
        print("XAUUSD FEATURE ENGINEERING PROJECT COMPLETE")
        print("🎯" * 20)
        print("\nComprehensive feature dataset generated and ready for")
        print("reinforcement learning model training!")
    else:
        print("\n❌ Dataset generation failed - check errors above")
