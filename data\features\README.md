
# XAUUSD Feature Dataset - Usage Guide

## Dataset Overview
- **Generation Date**: 2025-09-20T12:16:55.334001
- **Total Records**: 17,858
- **Date Range**: 2025-06-23 01:00:00 to 2025-09-19 23:55:00
- **Timeframe**: 5 minutes
- **Total Features**: 16
- **Quality Score**: 0.960

## Dataset Splits
- **Training**: 12,500 records (70%)
- **Validation**: 2,678 records (15%)
- **Test**: 2,680 records (15%)

## Files Generated
### Feature Files
- `train_features.csv` - Training features
- `val_features.csv` - Validation features  
- `test_features.csv` - Test features

### Target Files
- `train_targets.csv` - Training targets
- `val_targets.csv` - Validation targets
- `test_targets.csv` - Test targets

### Raw Data Files
- `train_raw.csv` - Raw OHLCV training data
- `val_raw.csv` - Raw OHLCV validation data
- `test_raw.csv` - Raw OHLCV test data

### Model Files
- `../models/xauusd_feature_pipeline.joblib` - Fitted feature pipeline

## Usage Example

```python
import pandas as pd
from pathlib import Path

# Load feature datasets
data_dir = Path("data/features")

train_features = pd.read_csv(data_dir / "train_features.csv", index_col=0)
train_targets = pd.read_csv(data_dir / "train_targets.csv", index_col=0)

val_features = pd.read_csv(data_dir / "val_features.csv", index_col=0)
val_targets = pd.read_csv(data_dir / "val_targets.csv", index_col=0)

# Load fitted pipeline for new data processing
from features.pipeline.production import ProductionFeaturePipeline

pipeline = ProductionFeaturePipeline()
pipeline.load_pipeline("data/models/xauusd_feature_pipeline.joblib")

# Generate features for new data
new_features = pipeline.generate_features(new_ohlcv_data)
```

## Feature Categories
The dataset includes 16 features across multiple categories:
- Multi-timeframe price analysis
- Technical indicators (RSI, MACD, Bollinger Bands, etc.)
- Volatility analysis (ATR, volatility expansion, etc.)
- Volume analysis (VWAP, OBV, volume trends, etc.)
- Cross-asset correlations (DXY, SPY, TLT, VIX, QQQ, IEF)
- Market regime detection
- Trading session analysis
- Temporal patterns

## Target Variables
The dataset includes 11 target variables:
- Price direction (next period)
- Price returns (multiple horizons)
- Volatility-adjusted returns
- Volume-weighted returns
- High-low range predictions

## Ready for Reinforcement Learning
This dataset is optimized for reinforcement learning model training with:
- Proper temporal splits (no data leakage)
- Comprehensive feature engineering
- Multiple target variables for different strategies
- Quality validation and monitoring
- Production-ready feature pipeline
