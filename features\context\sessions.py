"""
Trading Session Features Engine

Generates comprehensive trading session and temporal features including:
- Trading session analysis (Asian, European, US sessions)
- Temporal patterns (day of week, hour of day, month effects)
- Market timing features and session transitions
- Volume and volatility patterns by session
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, time
import pytz

# Import base classes
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from features.core.base_feature import BaseFeatureEngine, FeatureResult
from features.config.feature_config import FeatureConfig
from data_collection.config import Config


class TradingSessionFeatures(BaseFeatureEngine):
    """
    Trading session and temporal features engine.
    
    Generates comprehensive session-based and temporal features for XAUUSD trading
    including session analysis, temporal patterns, and market timing features.
    """
    
    def __init__(self, config: Config, feature_config: FeatureConfig, **kwargs):
        """
        Initialize trading session features engine.
        
        Args:
            config: Main system configuration
            feature_config: Feature-specific configuration
            **kwargs: Additional parameters
        """
        super().__init__(config)
        self.feature_config = feature_config
        self.context_config = feature_config.context_features
        
        # Session definitions (UTC times)
        self.sessions = {
            'asian': {'start': time(0, 0), 'end': time(9, 0)},      # Asian session
            'european': {'start': time(7, 0), 'end': time(16, 0)},  # European session
            'us': {'start': time(13, 0), 'end': time(22, 0)}        # US session
        }
        
        # Session overlap periods
        self.overlaps = {
            'asian_european': {'start': time(7, 0), 'end': time(9, 0)},
            'european_us': {'start': time(13, 0), 'end': time(16, 0)}
        }
        
        # Market holidays (simplified - major US holidays)
        self.holidays = [
            '2025-01-01',  # New Year's Day
            '2025-07-04',  # Independence Day
            '2025-12-25',  # Christmas Day
        ]
    
    def get_feature_type(self) -> str:
        """Get the type of features this engine generates."""
        return "trading_sessions"
    
    def get_required_columns(self) -> List[str]:
        """Get required columns for session analysis."""
        return ['open', 'high', 'low', 'close', 'volume']
    
    def get_feature_names(self) -> List[str]:
        """Get list of feature names this engine generates."""
        feature_names = []
        
        # Session indicators
        for session in self.sessions.keys():
            feature_names.extend([
                f"session_{session}",
                f"session_{session}_volume_ratio",
                f"session_{session}_volatility_ratio"
            ])
        
        # Session overlaps
        for overlap in self.overlaps.keys():
            feature_names.append(f"overlap_{overlap}")
        
        # Temporal features
        feature_names.extend([
            'hour_of_day',
            'day_of_week',
            'day_of_month',
            'month_of_year',
            'quarter_of_year',
            'is_weekend',
            'is_month_end',
            'is_quarter_end',
            'is_year_end',
            'is_holiday'
        ])
        
        # Session transition features
        feature_names.extend([
            'session_open',
            'session_close',
            'session_transition',
            'time_to_session_close',
            'time_from_session_open'
        ])
        
        # Market timing features
        feature_names.extend([
            'london_fix_proximity',
            'ny_fix_proximity',
            'market_open_proximity',
            'market_close_proximity'
        ])
        
        return feature_names
    
    def _generate_features_impl(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Implementation-specific feature generation logic.
        
        Args:
            data: Input OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with generated trading session features
        """
        # Ensure datetime index
        data = self._ensure_datetime_index(data)
        
        # Initialize features DataFrame
        features = pd.DataFrame(index=data.index)
        
        # Generate session features
        session_features = self._generate_session_features(data)
        features = pd.concat([features, session_features], axis=1)
        
        # Generate temporal features
        temporal_features = self._generate_temporal_features(data)
        features = pd.concat([features, temporal_features], axis=1)
        
        # Generate session transition features
        transition_features = self._generate_transition_features(data)
        features = pd.concat([features, transition_features], axis=1)
        
        # Generate market timing features
        timing_features = self._generate_timing_features(data)
        features = pd.concat([features, timing_features], axis=1)
        
        self.logger.info(f"Generated {len(features.columns)} trading session features")
        return features
    
    def _generate_session_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate trading session features."""
        session_df = pd.DataFrame(index=data.index)
        
        # Get UTC times
        utc_times = data.index.time
        
        # Calculate session indicators and statistics
        for session_name, session_times in self.sessions.items():
            try:
                # Session indicator
                session_mask = self._is_in_session(utc_times, session_times['start'], session_times['end'])
                session_df[f"session_{session_name}"] = session_mask.astype(int)
                
                # Session volume ratio (current volume vs session average)
                if 'volume' in data.columns:
                    session_volume_avg = data.loc[session_mask, 'volume'].rolling(window=20).mean()
                    current_volume = data['volume']
                    volume_ratio = current_volume / session_volume_avg.reindex(data.index, method='ffill')
                    session_df[f"session_{session_name}_volume_ratio"] = volume_ratio
                else:
                    session_df[f"session_{session_name}_volume_ratio"] = 0
                
                # Session volatility ratio
                session_returns = data['close'].pct_change()
                session_volatility = session_returns.loc[session_mask].rolling(window=20).std()
                current_volatility = session_returns.rolling(window=5).std()
                volatility_ratio = current_volatility / session_volatility.reindex(data.index, method='ffill')
                session_df[f"session_{session_name}_volatility_ratio"] = volatility_ratio
                
            except Exception as e:
                self.logger.warning(f"Error calculating session features for {session_name}: {str(e)}")
        
        # Session overlaps
        for overlap_name, overlap_times in self.overlaps.items():
            try:
                overlap_mask = self._is_in_session(utc_times, overlap_times['start'], overlap_times['end'])
                session_df[f"overlap_{overlap_name}"] = overlap_mask.astype(int)
            except Exception as e:
                self.logger.warning(f"Error calculating overlap features for {overlap_name}: {str(e)}")
        
        return session_df
    
    def _is_in_session(self, times: pd.Series, start_time: time, end_time: time) -> pd.Series:
        """Check if times are within a trading session."""
        if start_time <= end_time:
            # Normal session (doesn't cross midnight)
            return (times >= start_time) & (times <= end_time)
        else:
            # Session crosses midnight
            return (times >= start_time) | (times <= end_time)
    
    def _generate_temporal_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate temporal pattern features."""
        temporal_df = pd.DataFrame(index=data.index)
        
        try:
            # Basic temporal features
            temporal_df['hour_of_day'] = data.index.hour
            temporal_df['day_of_week'] = data.index.dayofweek  # 0=Monday, 6=Sunday
            temporal_df['day_of_month'] = data.index.day
            temporal_df['month_of_year'] = data.index.month
            temporal_df['quarter_of_year'] = data.index.quarter
            
            # Weekend indicator
            temporal_df['is_weekend'] = (data.index.dayofweek >= 5).astype(int)
            
            # Month/quarter/year end indicators
            temporal_df['is_month_end'] = self._is_month_end(data.index)
            temporal_df['is_quarter_end'] = self._is_quarter_end(data.index)
            temporal_df['is_year_end'] = self._is_year_end(data.index)
            
            # Holiday indicator
            temporal_df['is_holiday'] = self._is_holiday(data.index)
            
        except Exception as e:
            self.logger.warning(f"Error calculating temporal features: {str(e)}")
        
        return temporal_df
    
    def _is_month_end(self, dates: pd.DatetimeIndex) -> pd.Series:
        """Check if dates are within 3 days of month end."""
        month_ends = dates + pd.offsets.MonthEnd(0)
        days_to_month_end = (month_ends - dates).days
        return (days_to_month_end <= 3).astype(int)
    
    def _is_quarter_end(self, dates: pd.DatetimeIndex) -> pd.Series:
        """Check if dates are within 5 days of quarter end."""
        quarter_ends = dates + pd.offsets.QuarterEnd(0)
        days_to_quarter_end = (quarter_ends - dates).days
        return (days_to_quarter_end <= 5).astype(int)
    
    def _is_year_end(self, dates: pd.DatetimeIndex) -> pd.Series:
        """Check if dates are within 7 days of year end."""
        year_ends = dates + pd.offsets.YearEnd(0)
        days_to_year_end = (year_ends - dates).days
        return (days_to_year_end <= 7).astype(int)
    
    def _is_holiday(self, dates: pd.DatetimeIndex) -> pd.Series:
        """Check if dates are holidays or within 1 day of holidays."""
        holiday_dates = pd.to_datetime(self.holidays)
        is_holiday = pd.Series(0, index=dates)
        
        for holiday in holiday_dates:
            # Check if within 1 day of holiday
            days_diff = abs((dates.date - holiday.date()).days)
            is_holiday |= (days_diff <= 1)
        
        return is_holiday.astype(int)
    
    def _generate_transition_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate session transition features."""
        transition_df = pd.DataFrame(index=data.index)
        
        try:
            utc_times = data.index.time
            
            # Session open/close indicators
            session_opens = [time(0, 0), time(7, 0), time(13, 0)]  # Asian, European, US opens
            session_closes = [time(9, 0), time(16, 0), time(22, 0)]  # Session closes
            
            # Check for session opens (within 30 minutes)
            session_open = pd.Series(0, index=data.index)
            for open_time in session_opens:
                open_mask = self._is_near_time(utc_times, open_time, minutes=30)
                session_open |= open_mask
            transition_df['session_open'] = session_open.astype(int)
            
            # Check for session closes (within 30 minutes)
            session_close = pd.Series(0, index=data.index)
            for close_time in session_closes:
                close_mask = self._is_near_time(utc_times, close_time, minutes=30)
                session_close |= close_mask
            transition_df['session_close'] = session_close.astype(int)
            
            # Session transition (open or close)
            transition_df['session_transition'] = (session_open | session_close).astype(int)
            
            # Time to/from session events
            transition_df['time_to_session_close'] = self._time_to_next_session_close(data.index)
            transition_df['time_from_session_open'] = self._time_from_last_session_open(data.index)
            
        except Exception as e:
            self.logger.warning(f"Error calculating transition features: {str(e)}")
        
        return transition_df
    
    def _is_near_time(self, times: pd.Series, target_time: time, minutes: int = 30) -> pd.Series:
        """Check if times are within specified minutes of target time."""
        target_minutes = target_time.hour * 60 + target_time.minute
        current_minutes = times.map(lambda t: t.hour * 60 + t.minute)
        
        # Handle day boundary
        diff = abs(current_minutes - target_minutes)
        diff = np.minimum(diff, 1440 - diff)  # 1440 minutes in a day
        
        return diff <= minutes
    
    def _time_to_next_session_close(self, dates: pd.DatetimeIndex) -> pd.Series:
        """Calculate minutes to next session close."""
        session_closes = [9*60, 16*60, 22*60]  # Session closes in minutes
        
        result = pd.Series(0, index=dates)
        for i, dt in enumerate(dates):
            current_minutes = dt.hour * 60 + dt.minute
            
            # Find next session close
            next_closes = [sc for sc in session_closes if sc > current_minutes]
            if next_closes:
                result.iloc[i] = min(next_closes) - current_minutes
            else:
                # Next close is tomorrow's first session
                result.iloc[i] = (24*60) - current_minutes + session_closes[0]
        
        return result
    
    def _time_from_last_session_open(self, dates: pd.DatetimeIndex) -> pd.Series:
        """Calculate minutes from last session open."""
        session_opens = [0*60, 7*60, 13*60]  # Session opens in minutes
        
        result = pd.Series(0, index=dates)
        for i, dt in enumerate(dates):
            current_minutes = dt.hour * 60 + dt.minute
            
            # Find last session open
            last_opens = [so for so in session_opens if so <= current_minutes]
            if last_opens:
                result.iloc[i] = current_minutes - max(last_opens)
            else:
                # Last open was yesterday's last session
                result.iloc[i] = current_minutes + (24*60) - session_opens[-1]
        
        return result
    
    def _generate_timing_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate market timing features."""
        timing_df = pd.DataFrame(index=data.index)
        
        try:
            utc_times = data.index.time
            
            # London PM Fix (15:00 UTC)
            london_fix_time = time(15, 0)
            timing_df['london_fix_proximity'] = self._proximity_to_time(utc_times, london_fix_time)
            
            # NY Close Fix (21:00 UTC)
            ny_fix_time = time(21, 0)
            timing_df['ny_fix_proximity'] = self._proximity_to_time(utc_times, ny_fix_time)
            
            # Market open proximity (major session opens)
            market_opens = [time(0, 0), time(7, 0), time(13, 0)]
            market_open_prox = pd.Series(0.0, index=data.index)
            for open_time in market_opens:
                prox = self._proximity_to_time(utc_times, open_time)
                market_open_prox = np.maximum(market_open_prox, prox)
            timing_df['market_open_proximity'] = market_open_prox
            
            # Market close proximity (major session closes)
            market_closes = [time(9, 0), time(16, 0), time(22, 0)]
            market_close_prox = pd.Series(0.0, index=data.index)
            for close_time in market_closes:
                prox = self._proximity_to_time(utc_times, close_time)
                market_close_prox = np.maximum(market_close_prox, prox)
            timing_df['market_close_proximity'] = market_close_prox
            
        except Exception as e:
            self.logger.warning(f"Error calculating timing features: {str(e)}")
        
        return timing_df
    
    def _proximity_to_time(self, times: pd.Series, target_time: time, max_minutes: int = 60) -> pd.Series:
        """Calculate proximity to target time (1.0 = exact match, 0.0 = far away)."""
        target_minutes = target_time.hour * 60 + target_time.minute
        current_minutes = times.map(lambda t: t.hour * 60 + t.minute)
        
        # Calculate minimum distance (handling day boundary)
        diff = abs(current_minutes - target_minutes)
        diff = np.minimum(diff, 1440 - diff)  # 1440 minutes in a day
        
        # Convert to proximity (1.0 at target, 0.0 at max_minutes away)
        proximity = np.maximum(0.0, 1.0 - (diff / max_minutes))
        
        return pd.Series(proximity, index=times.index)
