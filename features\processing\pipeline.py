"""
Feature Processing Pipeline

Comprehensive feature processing pipeline that combines:
- Feature scaling and normalization
- Feature selection and redundancy elimination
- Data quality assessment
- Pipeline optimization and validation
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path
import joblib
from datetime import datetime

# Import base classes
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))
from features.core.base_feature import BaseFeatureEngine, FeatureResult
from features.config.feature_config import FeatureConfig
from data_collection.config import Config
from .scaling import FeatureScaler, FeatureScalingEngine
from .selection import FeatureSelector, FeatureSelectionEngine


class FeatureProcessingPipeline:
    """
    Comprehensive feature processing pipeline for XAUUSD trading features.
    
    Combines scaling, selection, and quality assessment in a unified pipeline
    optimized for reinforcement learning model training.
    """
    
    def __init__(self,
                 scaling_method: str = 'auto',
                 correlation_threshold: float = 0.95,
                 variance_threshold: float = 0.01,
                 max_features: Optional[int] = None,
                 handle_outliers: bool = True):
        """
        Initialize feature processing pipeline.
        
        Args:
            scaling_method: Method for feature scaling ('auto', 'standard', 'robust', etc.)
            correlation_threshold: Threshold for correlation-based redundancy elimination
            variance_threshold: Minimum variance threshold for features
            max_features: Maximum number of features to select
            handle_outliers: Whether to use outlier-robust processing
        """
        self.scaling_method = scaling_method
        self.correlation_threshold = correlation_threshold
        self.variance_threshold = variance_threshold
        self.max_features = max_features
        self.handle_outliers = handle_outliers
        
        # Pipeline components
        self.scaler_ = None
        self.selector_ = None
        
        # Pipeline state
        self.is_fitted_ = False
        self.feature_names_in_ = None
        self.feature_names_out_ = None
        self.processing_stats_ = {}
        
    def fit(self, X: pd.DataFrame, y: Optional[pd.Series] = None) -> 'FeatureProcessingPipeline':
        """
        Fit the processing pipeline to the data.
        
        Args:
            X: Input features DataFrame
            y: Target variable (optional, for supervised feature selection)
            
        Returns:
            Fitted pipeline instance
        """
        self.feature_names_in_ = list(X.columns)
        
        # Step 1: Feature Selection (before scaling to avoid scaling noise)
        print("Step 1: Feature Selection...")
        self.selector_ = FeatureSelector(
            correlation_threshold=self.correlation_threshold,
            variance_threshold=self.variance_threshold,
            max_features=self.max_features,
            selection_methods=['variance', 'correlation']
        )
        
        X_selected = self.selector_.fit_select(X, y)
        self.feature_names_out_ = list(X_selected.columns)

        selection_summary = self.selector_.get_selection_summary()
        print(f"  Selected {selection_summary['selected_features']} features from {selection_summary['total_features_input']}")
        print(f"  Eliminated {selection_summary['eliminated_features']} features")

        # Handle case where no features are selected
        if len(self.feature_names_out_) == 0:
            print("  WARNING: No features selected! Using top 10 features by variance as fallback.")

            # Fallback: Select top features by variance
            numerical_features = X.select_dtypes(include=[np.number]).columns
            if len(numerical_features) > 0:
                # Clean data for variance calculation
                X_clean = X[numerical_features].replace([np.inf, -np.inf], np.nan)
                for col in X_clean.columns:
                    col_series = X_clean[col]
                    # Check if all values are NaN - ensure we get a boolean scalar
                    try:
                        all_nan = bool(col_series.isna().all())
                    except:
                        # Fallback: check if series is empty or all values are NaN
                        all_nan = len(col_series.dropna()) == 0

                    if all_nan:
                        X_clean[col] = 0
                    else:
                        median_val = col_series.median()
                        # Ensure median_val is a scalar
                        if hasattr(median_val, 'iloc'):
                            median_val = median_val.iloc[0] if len(median_val) > 0 else 0

                        # Check if median is NaN
                        try:
                            is_nan = bool(pd.isna(median_val))
                        except:
                            is_nan = True

                        if is_nan:
                            median_val = 0
                        X_clean[col] = col_series.fillna(median_val)

                # Calculate variances and select top features
                variances = X_clean.var().sort_values(ascending=False)
                valid_variances = variances[~pd.isna(variances) & (variances > 0)]

                # Select top 10 features or all available if less than 10
                n_features = min(10, len(valid_variances))
                if n_features > 0:
                    top_features = valid_variances.head(n_features).index.tolist()
                    self.feature_names_out_ = top_features
                    X_selected = X[self.feature_names_out_]
                    print(f"  Fallback: Selected {len(self.feature_names_out_)} features by variance")
                else:
                    raise ValueError("No valid features found for processing")
            else:
                raise ValueError("No numerical features found for processing")

        # Step 2: Feature Scaling
        print("Step 2: Feature Scaling...")
        self.scaler_ = FeatureScaler(
            method=self.scaling_method,
            handle_outliers=self.handle_outliers,
            time_aware=True
        )

        self.scaler_.fit(X_selected)
        scaling_info = self.scaler_.get_feature_info()

        method_counts = {}
        if not scaling_info.empty:
            method_counts = scaling_info['scaling_method'].value_counts()
            print(f"  Scaling methods used: {dict(method_counts)}")

        # Store processing statistics
        self.processing_stats_ = {
            'input_features': len(self.feature_names_in_),
            'output_features': len(self.feature_names_out_),
            'reduction_ratio': 1 - (len(self.feature_names_out_) / len(self.feature_names_in_)),
            'selection_summary': selection_summary,
            'scaling_methods': dict(method_counts) if not scaling_info.empty else {}
        }
        
        self.is_fitted_ = True
        print(f"Pipeline fitted successfully!")
        print(f"  Feature reduction: {len(self.feature_names_in_)} → {len(self.feature_names_out_)} ({self.processing_stats_['reduction_ratio']:.1%} reduction)")
        
        return self
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Transform features using fitted pipeline.
        
        Args:
            X: Input features DataFrame
            
        Returns:
            Processed features DataFrame
        """
        if not self.is_fitted_:
            raise ValueError("Pipeline must be fitted before transform")
        
        # Step 1: Feature Selection
        X_selected = X[self.feature_names_out_]
        
        # Step 2: Feature Scaling
        X_processed = self.scaler_.transform(X_selected)
        
        return X_processed
    
    def fit_transform(self, X: pd.DataFrame, y: Optional[pd.Series] = None) -> pd.DataFrame:
        """Fit and transform in one step."""
        return self.fit(X, y).transform(X)
    
    def get_feature_importance_analysis(self) -> pd.DataFrame:
        """Get comprehensive feature importance analysis."""
        if not self.is_fitted_:
            return pd.DataFrame()
        
        # Combine selection scores and scaling info
        analysis_data = []
        
        # Get scaling info
        scaling_info = self.scaler_.get_feature_info()
        scaling_dict = scaling_info.set_index('feature').to_dict('index') if not scaling_info.empty else {}
        
        # Get selection scores
        selection_scores = self.selector_.feature_scores_
        
        for feature in self.feature_names_out_:
            feature_data = {
                'feature': feature,
                'selected': True,
                'scaling_method': scaling_dict.get(feature, {}).get('scaling_method', 'unknown'),
                'outlier_ratio': scaling_dict.get(feature, {}).get('outlier_ratio', 0),
                'skewness': scaling_dict.get(feature, {}).get('skewness', 0)
            }
            
            # Add selection scores
            for score_name, scores in selection_scores.items():
                feature_data[f'selection_{score_name}'] = scores.get(feature, 0)
            
            analysis_data.append(feature_data)
        
        # Add eliminated features
        for feature, reason in self.selector_.elimination_reasons_.items():
            feature_data = {
                'feature': feature,
                'selected': False,
                'elimination_reason': reason,
                'scaling_method': 'not_scaled'
            }
            analysis_data.append(feature_data)
        
        return pd.DataFrame(analysis_data)
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """Get comprehensive processing summary."""
        if not self.is_fitted_:
            return {}
        
        summary = self.processing_stats_.copy()
        
        # Add feature categories analysis
        if self.feature_names_out_:
            categories = self._categorize_features(self.feature_names_out_)
            summary['feature_categories'] = categories
        
        return summary
    
    def _categorize_features(self, features: List[str]) -> Dict[str, int]:
        """Categorize features by type."""
        categories = {
            'price': 0, 'technical': 0, 'volatility': 0, 'volume': 0,
            'correlation': 0, 'context': 0, 'other': 0
        }
        
        for feature in features:
            feature_lower = feature.lower()
            if any(x in feature_lower for x in ['return', 'close_', 'high_', 'low_', 'price']):
                categories['price'] += 1
            elif any(x in feature_lower for x in ['rsi', 'macd', 'stoch', 'williams']):
                categories['technical'] += 1
            elif any(x in feature_lower for x in ['bb_', 'atr', 'vol_']):
                categories['volatility'] += 1
            elif any(x in feature_lower for x in ['vwap', 'obv', 'volume']):
                categories['volume'] += 1
            elif any(x in feature_lower for x in ['corr_', 'correlation']):
                categories['correlation'] += 1
            elif any(x in feature_lower for x in ['session', 'hour', 'day', 'week', 'month']):
                categories['context'] += 1
            else:
                categories['other'] += 1
        
        return categories
    
    def save_pipeline(self, filepath: str):
        """Save fitted pipeline to file."""
        if not self.is_fitted_:
            raise ValueError("Pipeline must be fitted before saving")
        
        pipeline_data = {
            'scaler': self.scaler_,
            'selector': self.selector_,
            'feature_names_in': self.feature_names_in_,
            'feature_names_out': self.feature_names_out_,
            'processing_stats': self.processing_stats_,
            'config': {
                'scaling_method': self.scaling_method,
                'correlation_threshold': self.correlation_threshold,
                'variance_threshold': self.variance_threshold,
                'max_features': self.max_features,
                'handle_outliers': self.handle_outliers
            }
        }
        
        joblib.dump(pipeline_data, filepath)
        print(f"Pipeline saved to {filepath}")
    
    def load_pipeline(self, filepath: str):
        """Load fitted pipeline from file."""
        pipeline_data = joblib.load(filepath)
        
        self.scaler_ = pipeline_data['scaler']
        self.selector_ = pipeline_data['selector']
        self.feature_names_in_ = pipeline_data['feature_names_in']
        self.feature_names_out_ = pipeline_data['feature_names_out']
        self.processing_stats_ = pipeline_data['processing_stats']
        
        # Update config
        config = pipeline_data['config']
        self.scaling_method = config['scaling_method']
        self.correlation_threshold = config['correlation_threshold']
        self.variance_threshold = config['variance_threshold']
        self.max_features = config['max_features']
        self.handle_outliers = config['handle_outliers']
        
        self.is_fitted_ = True
        print(f"Pipeline loaded from {filepath}")


class FeatureProcessingEngine(BaseFeatureEngine):
    """
    Feature processing engine that integrates with the feature engineering pipeline.
    
    Provides comprehensive feature processing including scaling, selection,
    and optimization for reinforcement learning model training.
    """
    
    def __init__(self, config: Config, feature_config: FeatureConfig, **kwargs):
        """
        Initialize feature processing engine.
        
        Args:
            config: Main system configuration
            feature_config: Feature-specific configuration
            **kwargs: Additional parameters
        """
        super().__init__(config)
        self.feature_config = feature_config
        self.processing_config = feature_config.processing
        
        # Pipeline configuration
        self.scaling_method = self.processing_config.get('scaling', {}).get('default_method', 'auto')
        self.correlation_threshold = self.processing_config.get('selection', {}).get('correlation_threshold', 0.95)
        self.variance_threshold = self.processing_config.get('selection', {}).get('variance_threshold', 0.01)
        self.max_features = self.processing_config.get('selection', {}).get('max_features', None)
        
        # Fitted pipeline
        self.pipeline_ = None
        
    def get_feature_type(self) -> str:
        """Get the type of processing this engine performs."""
        return "feature_processing"
    
    def get_required_columns(self) -> List[str]:
        """Get required columns (accepts any columns)."""
        return []  # Can work with any features
    
    def get_feature_names(self) -> List[str]:
        """Get list of feature names (determined after processing)."""
        if self.pipeline_ and self.pipeline_.is_fitted_:
            return self.pipeline_.feature_names_out_
        return []
    
    def process_features(self, data: pd.DataFrame, target: Optional[pd.Series] = None, **kwargs) -> pd.DataFrame:
        """
        Process features using comprehensive pipeline.
        
        Args:
            data: Input features DataFrame
            target: Target variable for supervised processing
            **kwargs: Additional parameters
            
        Returns:
            Processed features DataFrame
        """
        # Ensure datetime index
        data = self._ensure_datetime_index(data)
        
        # Create and fit pipeline
        self.pipeline_ = FeatureProcessingPipeline(
            scaling_method=self.scaling_method,
            correlation_threshold=self.correlation_threshold,
            variance_threshold=self.variance_threshold,
            max_features=self.max_features,
            handle_outliers=True
        )
        
        processed_data = self.pipeline_.fit_transform(data, target)
        
        self.logger.info(f"Processed {len(data.columns)} features → {len(processed_data.columns)} features")
        
        return processed_data
    
    def _generate_features_impl(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Implementation for feature generation interface.
        For processing, this performs comprehensive feature processing.
        """
        target = kwargs.get('target', None)
        return self.process_features(data, target, **kwargs)
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """Get processing summary."""
        if self.pipeline_ is None:
            return {}
        return self.pipeline_.get_processing_summary()
    
    def get_feature_analysis(self) -> pd.DataFrame:
        """Get feature importance analysis."""
        if self.pipeline_ is None:
            return pd.DataFrame()
        return self.pipeline_.get_feature_importance_analysis()
    
    def save_pipeline(self, filepath: str):
        """Save processing pipeline."""
        if self.pipeline_ is not None:
            self.pipeline_.save_pipeline(filepath)
    
    def load_pipeline(self, filepath: str):
        """Load processing pipeline."""
        self.pipeline_ = FeatureProcessingPipeline()
        self.pipeline_.load_pipeline(filepath)
