"""
Feature Quality Validation Framework

Provides comprehensive data quality assessment for feature engineering including:
- Feature quality metrics and scoring
- Distribution analysis and outlier detection
- Missing value pattern analysis
- Feature stability and information content assessment
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import warnings
from scipy import stats
from sklearn.feature_selection import mutual_info_regression

# Import base classes
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from data_collection.config import Config
from data_collection.error_handling.logger import LoggerMixin


class FeatureQualityValidator(LoggerMixin):
    """
    Comprehensive feature quality validation framework.
    
    Provides detailed analysis of feature quality including distribution analysis,
    missing value patterns, stability metrics, and information content assessment.
    """
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize feature quality validator.
        
        Args:
            config: System configuration (optional)
        """
        super().__init__()
        self.config = config or Config()
        
        # Quality thresholds
        self.missing_threshold = 0.1  # 10% missing values threshold
        self.outlier_threshold = 0.05  # 5% outliers threshold
        self.stability_threshold = 0.8  # 80% stability threshold
        self.correlation_threshold = 0.95  # 95% correlation threshold

    def _safe_float_conversion(self, value):
        """Safely convert a value to float, handling Series and other edge cases."""
        try:
            if hasattr(value, 'iloc'):
                # It's a Series, take the first value
                return float(value.iloc[0]) if len(value) > 0 else 0.0
            elif hasattr(value, '__float__'):
                return float(value)
            else:
                return 0.0
        except (TypeError, ValueError, IndexError):
            return 0.0

    def _safe_int_conversion(self, value):
        """Safely convert a value to int, handling Series and other edge cases."""
        try:
            if hasattr(value, 'iloc'):
                # It's a Series, take the first value
                return int(value.iloc[0]) if len(value) > 0 else 0
            elif hasattr(value, '__int__'):
                return int(value)
            else:
                return 0
        except (TypeError, ValueError, IndexError):
            return 0

    def validate_features(self, features: pd.DataFrame, target: Optional[pd.Series] = None) -> Dict[str, Any]:
        """
        Perform comprehensive feature quality validation.
        
        Args:
            features: Feature DataFrame to validate
            target: Target variable for supervised validation (optional)
            
        Returns:
            Comprehensive validation report
        """
        self.logger.info(f"Starting feature quality validation for {features.shape[1]} features")
        
        validation_report = {
            'timestamp': datetime.now().isoformat(),
            'feature_count': len(features.columns),
            'record_count': len(features),
            'overall_quality_score': 0.0,
            'quality_metrics': {},
            'feature_analysis': {},
            'recommendations': []
        }
        
        # Basic quality metrics
        validation_report['quality_metrics'] = self._calculate_quality_metrics(features)
        
        # Individual feature analysis
        validation_report['feature_analysis'] = self._analyze_individual_features(features, target)
        
        # Calculate overall quality score
        validation_report['overall_quality_score'] = self._calculate_overall_quality_score(
            validation_report['quality_metrics'], validation_report['feature_analysis']
        )
        
        # Generate recommendations
        validation_report['recommendations'] = self._generate_recommendations(
            validation_report['quality_metrics'], validation_report['feature_analysis']
        )
        
        self.logger.info(f"Feature quality validation completed. Overall score: {validation_report['overall_quality_score']:.3f}")
        
        return validation_report
    
    def _calculate_quality_metrics(self, features: pd.DataFrame) -> Dict[str, Any]:
        """Calculate overall quality metrics for the feature set."""
        metrics = {}
        
        # Missing value analysis
        missing_counts = features.isnull().sum()
        missing_ratios = missing_counts / len(features)
        
        metrics['missing_values'] = {
            'total_missing': int(missing_counts.sum()),
            'features_with_missing': int((missing_counts > 0).sum()),
            'max_missing_ratio': float(missing_ratios.max()),
            'avg_missing_ratio': float(missing_ratios.mean()),
            'features_above_threshold': int((missing_ratios > self.missing_threshold).sum())
        }
        
        # Data type analysis
        numerical_features = features.select_dtypes(include=[np.number]).columns
        categorical_features = features.select_dtypes(exclude=[np.number]).columns
        
        metrics['data_types'] = {
            'numerical_features': len(numerical_features),
            'categorical_features': len(categorical_features),
            'numerical_ratio': len(numerical_features) / len(features.columns)
        }
        
        # Infinite value analysis
        if len(numerical_features) > 0:
            inf_counts = np.isinf(features[numerical_features]).sum()
            metrics['infinite_values'] = {
                'total_infinite': int(inf_counts.sum()),
                'features_with_infinite': int((inf_counts > 0).sum()),
                'max_infinite_count': int(inf_counts.max())
            }
        
        # Variance analysis
        if len(numerical_features) > 0:
            variances = features[numerical_features].var()
            zero_variance_features = (variances == 0).sum()
            low_variance_features = (variances < 0.01).sum()
            
            metrics['variance_analysis'] = {
                'zero_variance_features': int(zero_variance_features),
                'low_variance_features': int(low_variance_features),
                'avg_variance': float(variances.mean()),
                'variance_range': [float(variances.min()), float(variances.max())]
            }
        
        # Correlation analysis
        if len(numerical_features) > 1:
            try:
                corr_matrix = features[numerical_features].corr().abs()
                # Get upper triangle (excluding diagonal)
                upper_triangle = corr_matrix.where(
                    np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
                )
                
                high_corr_pairs = (upper_triangle > self.correlation_threshold).sum().sum()
                max_correlation = upper_triangle.max().max()
                avg_correlation = upper_triangle.mean().mean()
                
                metrics['correlation_analysis'] = {
                    'high_correlation_pairs': int(high_corr_pairs),
                    'max_correlation': float(max_correlation) if not np.isnan(max_correlation) else 0.0,
                    'avg_correlation': float(avg_correlation) if not np.isnan(avg_correlation) else 0.0
                }
            except Exception as e:
                self.logger.warning(f"Correlation analysis failed: {str(e)}")
                metrics['correlation_analysis'] = {'error': str(e)}
        
        return metrics
    
    def _analyze_individual_features(self, features: pd.DataFrame, target: Optional[pd.Series] = None) -> Dict[str, Dict[str, Any]]:
        """Analyze individual feature quality metrics."""
        feature_analysis = {}
        
        for column in features.columns:
            analysis = {
                'data_type': str(features[column].dtype) if hasattr(features[column], 'dtype') else 'unknown',
                'missing_ratio': self._safe_float_conversion(features[column].isnull().mean()),
                'unique_values': self._safe_int_conversion(features[column].nunique()),
                'unique_ratio': self._safe_float_conversion(features[column].nunique() / len(features)) if len(features) > 0 else 0.0
            }
            
            # Numerical feature analysis
            if pd.api.types.is_numeric_dtype(features[column]):
                series = features[column].dropna()
                
                if len(series) > 0:
                    try:
                        analysis.update({
                            'mean': float(series.mean()) if not pd.isna(series.mean()) else 0.0,
                            'std': float(series.std()) if not pd.isna(series.std()) else 0.0,
                            'min': float(series.min()) if not pd.isna(series.min()) else 0.0,
                            'max': float(series.max()) if not pd.isna(series.max()) else 0.0,
                            'skewness': float(series.skew()) if not pd.isna(series.skew()) else 0.0,
                            'kurtosis': float(series.kurtosis()) if not pd.isna(series.kurtosis()) else 0.0,
                            'variance': float(series.var()) if not pd.isna(series.var()) else 0.0,
                            'infinite_count': int(np.isinf(series).sum()) if hasattr(np.isinf(series).sum(), '__int__') else 0
                        })
                    except (TypeError, ValueError):
                        # Fallback values if conversion fails
                        analysis.update({
                            'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0,
                            'skewness': 0.0, 'kurtosis': 0.0, 'variance': 0.0, 'infinite_count': 0
                        })
                    
                    # Outlier analysis using IQR method
                    Q1 = series.quantile(0.25)
                    Q3 = series.quantile(0.75)
                    IQR = Q3 - Q1
                    
                    if IQR > 0:
                        lower_bound = Q1 - 1.5 * IQR
                        upper_bound = Q3 + 1.5 * IQR
                        outliers = ((series < lower_bound) | (series > upper_bound)).sum()
                        analysis['outlier_ratio'] = float(outliers / len(series))
                    else:
                        analysis['outlier_ratio'] = 0.0
                    
                    # Information content (if target provided)
                    if target is not None and len(series) > 10:
                        try:
                            # Align series and target
                            common_index = series.index.intersection(target.index)
                            if len(common_index) > 10:
                                aligned_series = series.loc[common_index]
                                aligned_target = target.loc[common_index]
                                
                                # Remove any remaining NaN values
                                valid_mask = aligned_series.notna() & aligned_target.notna()
                                if valid_mask.sum() > 10:
                                    clean_series = aligned_series[valid_mask]
                                    clean_target = aligned_target[valid_mask]
                                    
                                    # Calculate mutual information
                                    mi_score = mutual_info_regression(
                                        clean_series.values.reshape(-1, 1), 
                                        clean_target.values,
                                        random_state=42
                                    )[0]
                                    analysis['mutual_information'] = float(mi_score)
                                    
                                    # Calculate correlation with target
                                    correlation = clean_series.corr(clean_target)
                                    analysis['target_correlation'] = float(correlation) if not np.isnan(correlation) else 0.0
                        except Exception as e:
                            self.logger.debug(f"Information content analysis failed for {column}: {str(e)}")
            
            # Quality score for individual feature
            analysis['quality_score'] = self._calculate_feature_quality_score(analysis)
            
            feature_analysis[column] = analysis
        
        return feature_analysis
    
    def _calculate_feature_quality_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate quality score for individual feature (0-1 scale)."""
        score = 1.0
        
        # Penalize high missing values
        missing_ratio = analysis.get('missing_ratio', 0)
        if missing_ratio > self.missing_threshold:
            score *= (1 - missing_ratio)
        
        # Penalize zero variance
        variance = analysis.get('variance', 1)
        if variance == 0:
            score *= 0.1
        elif variance < 0.01:
            score *= 0.5
        
        # Penalize high outlier ratio
        outlier_ratio = analysis.get('outlier_ratio', 0)
        if outlier_ratio > self.outlier_threshold:
            score *= (1 - outlier_ratio * 0.5)
        
        # Penalize infinite values
        infinite_count = analysis.get('infinite_count', 0)
        if infinite_count > 0:
            score *= 0.8
        
        # Reward information content
        mutual_info = analysis.get('mutual_information', 0)
        if mutual_info > 0:
            score *= (1 + mutual_info * 0.2)  # Boost up to 20%
        
        return max(0.0, min(1.0, score))
    
    def _calculate_overall_quality_score(self, quality_metrics: Dict[str, Any], feature_analysis: Dict[str, Dict[str, Any]]) -> float:
        """Calculate overall quality score for the entire feature set."""
        # Average individual feature scores
        feature_scores = [analysis['quality_score'] for analysis in feature_analysis.values()]
        avg_feature_score = np.mean(feature_scores) if feature_scores else 0.0
        
        # Penalize overall issues
        overall_score = avg_feature_score
        
        # Penalize high missing value ratio
        missing_metrics = quality_metrics.get('missing_values', {})
        if missing_metrics.get('features_above_threshold', 0) > 0:
            penalty = missing_metrics.get('features_above_threshold', 0) / len(feature_analysis)
            overall_score *= (1 - penalty * 0.3)
        
        # Penalize high correlation
        corr_metrics = quality_metrics.get('correlation_analysis', {})
        if corr_metrics.get('high_correlation_pairs', 0) > 0:
            penalty = min(0.2, corr_metrics.get('high_correlation_pairs', 0) / 100)
            overall_score *= (1 - penalty)
        
        # Penalize infinite values
        inf_metrics = quality_metrics.get('infinite_values', {})
        if inf_metrics.get('total_infinite', 0) > 0:
            overall_score *= 0.9
        
        return max(0.0, min(1.0, overall_score))
    
    def _generate_recommendations(self, quality_metrics: Dict[str, Any], feature_analysis: Dict[str, Dict[str, Any]]) -> List[str]:
        """Generate actionable recommendations based on quality analysis."""
        recommendations = []
        
        # Missing value recommendations
        missing_metrics = quality_metrics.get('missing_values', {})
        if missing_metrics.get('features_above_threshold', 0) > 0:
            recommendations.append(
                f"Consider imputing or removing {missing_metrics['features_above_threshold']} features "
                f"with >10% missing values"
            )
        
        # Variance recommendations
        variance_metrics = quality_metrics.get('variance_analysis', {})
        if variance_metrics.get('zero_variance_features', 0) > 0:
            recommendations.append(
                f"Remove {variance_metrics['zero_variance_features']} zero-variance features"
            )
        
        if variance_metrics.get('low_variance_features', 0) > 0:
            recommendations.append(
                f"Consider removing {variance_metrics['low_variance_features']} low-variance features"
            )
        
        # Correlation recommendations
        corr_metrics = quality_metrics.get('correlation_analysis', {})
        if corr_metrics.get('high_correlation_pairs', 0) > 0:
            recommendations.append(
                f"Remove redundant features: {corr_metrics['high_correlation_pairs']} highly correlated pairs found"
            )
        
        # Infinite value recommendations
        inf_metrics = quality_metrics.get('infinite_values', {})
        if inf_metrics.get('total_infinite', 0) > 0:
            recommendations.append(
                f"Handle {inf_metrics['total_infinite']} infinite values in {inf_metrics['features_with_infinite']} features"
            )
        
        # Individual feature recommendations
        low_quality_features = [
            name for name, analysis in feature_analysis.items()
            if analysis['quality_score'] < 0.5
        ]
        
        if low_quality_features:
            recommendations.append(
                f"Review {len(low_quality_features)} low-quality features: {low_quality_features[:5]}..."
            )
        
        # Information content recommendations
        if any('mutual_information' in analysis for analysis in feature_analysis.values()):
            high_info_features = [
                name for name, analysis in feature_analysis.items()
                if analysis.get('mutual_information', 0) > 0.1
            ]
            
            if high_info_features:
                recommendations.append(
                    f"Prioritize {len(high_info_features)} high-information features for model training"
                )
        
        return recommendations
    
    def generate_quality_report(self, validation_result: Dict[str, Any]) -> str:
        """Generate a human-readable quality report."""
        report = []
        report.append("=" * 60)
        report.append("FEATURE QUALITY VALIDATION REPORT")
        report.append("=" * 60)
        
        # Overall summary
        report.append(f"Timestamp: {validation_result['timestamp']}")
        report.append(f"Features: {validation_result['feature_count']}")
        report.append(f"Records: {validation_result['record_count']}")
        report.append(f"Overall Quality Score: {validation_result['overall_quality_score']:.3f}/1.000")
        report.append("")
        
        # Quality metrics
        metrics = validation_result['quality_metrics']
        report.append("QUALITY METRICS:")
        
        if 'missing_values' in metrics:
            mv = metrics['missing_values']
            report.append(f"  Missing Values: {mv['total_missing']} total, {mv['features_with_missing']} features affected")
            report.append(f"  Max Missing Ratio: {mv['max_missing_ratio']:.1%}")
        
        if 'data_types' in metrics:
            dt = metrics['data_types']
            report.append(f"  Data Types: {dt['numerical_features']} numerical, {dt['categorical_features']} categorical")
        
        if 'infinite_values' in metrics:
            iv = metrics['infinite_values']
            if iv['total_infinite'] > 0:
                report.append(f"  Infinite Values: {iv['total_infinite']} total in {iv['features_with_infinite']} features")
        
        if 'correlation_analysis' in metrics:
            ca = metrics['correlation_analysis']
            if 'high_correlation_pairs' in ca:
                report.append(f"  High Correlations: {ca['high_correlation_pairs']} pairs above {self.correlation_threshold:.0%}")
        
        report.append("")
        
        # Recommendations
        if validation_result['recommendations']:
            report.append("RECOMMENDATIONS:")
            for i, rec in enumerate(validation_result['recommendations'], 1):
                report.append(f"  {i}. {rec}")
        
        report.append("=" * 60)
        
        return "\n".join(report)
