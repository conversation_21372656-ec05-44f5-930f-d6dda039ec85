"""
Production Feature Generation Pipeline

Provides a production-ready feature generation pipeline optimized for:
- Real-time and batch feature generation
- Performance monitoring and optimization
- Error handling and recovery
- Caching and incremental updates
- Integration with trading systems
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Union, Callable
from datetime import datetime, timedelta
from pathlib import Path
import joblib
import json

# Import base classes and components
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))
from features.core.factories import FeatureEngineFactory, FeatureEngineOrchestrator, FeatureType
from features.config.feature_config import FeatureConfig
from features.processing.pipeline import FeatureProcessingPipeline
from features.validation.quality import FeatureQualityValidator
from features.validation.performance import PerformanceBenchmarker
from data_collection.config import Config
from data_collection.error_handling.logger import LoggerMixin


class ProductionFeaturePipeline(LoggerMixin):
    """
    Production-ready feature generation pipeline for XAUUSD trading.
    
    Provides comprehensive feature generation with performance monitoring,
    quality validation, error handling, and optimization for real-time trading.
    """
    
    def __init__(self, config: Optional[Config] = None, feature_config: Optional[FeatureConfig] = None):
        """
        Initialize production feature pipeline.
        
        Args:
            config: System configuration
            feature_config: Feature-specific configuration
        """
        super().__init__()
        
        self.config = config or Config()
        self.feature_config = feature_config or FeatureConfig(self.config)
        
        # Core components
        self.factory = FeatureEngineFactory(self.config, self.feature_config)
        self.orchestrator = FeatureEngineOrchestrator(self.config, self.feature_config)
        self.processing_pipeline = None
        
        # Validation and monitoring
        self.quality_validator = FeatureQualityValidator(self.config)
        self.performance_benchmarker = PerformanceBenchmarker(self.config)
        
        # Pipeline state
        self.is_fitted = False
        self.last_generation_time = None
        self.feature_cache = {}
        self.processing_stats = {}
        
        # Configuration
        self.enable_caching = True
        self.enable_quality_validation = True
        self.enable_performance_monitoring = True
        self.max_cache_age_hours = 24
        
    def fit(self, training_data: pd.DataFrame, target: Optional[pd.Series] = None) -> 'ProductionFeaturePipeline':
        """
        Fit the production pipeline on training data.
        
        Args:
            training_data: Historical OHLCV data for fitting
            target: Target variable for supervised feature processing
            
        Returns:
            Fitted pipeline instance
        """
        self.logger.info("Fitting production feature pipeline")
        
        with self.performance_benchmarker.monitor_operation("pipeline_fitting", len(training_data)):
            # Generate comprehensive features
            self.logger.info("Generating comprehensive feature set for fitting")
            feature_results = self.orchestrator.generate_all_features(training_data)
            
            if not feature_results:
                raise ValueError("No features generated during fitting")
            
            # Combine features
            combined_features = self.orchestrator.combine_features(feature_results)
            
            if combined_features.empty:
                raise ValueError("Combined features are empty")
            
            self.logger.info(f"Generated {len(combined_features.columns)} features for fitting")
            
            # Fit processing pipeline
            self.logger.info("Fitting feature processing pipeline")
            self.processing_pipeline = FeatureProcessingPipeline(
                scaling_method='auto',
                correlation_threshold=0.95,
                variance_threshold=0.01,
                max_features=150,  # Optimal for RL
                handle_outliers=True
            )
            
            processed_features = self.processing_pipeline.fit_transform(combined_features, target)
            
            # Validate quality
            if self.enable_quality_validation:
                self.logger.info("Validating feature quality")
                quality_report = self.quality_validator.validate_features(processed_features, target)
                
                if quality_report['overall_quality_score'] < 0.5:
                    self.logger.warning(f"Low feature quality score: {quality_report['overall_quality_score']:.3f}")
                else:
                    self.logger.info(f"Feature quality score: {quality_report['overall_quality_score']:.3f}")
            
            self.is_fitted = True
            self.logger.info("Production pipeline fitted successfully")
        
        return self
    
    def generate_features(self, data: pd.DataFrame, use_cache: bool = None) -> pd.DataFrame:
        """
        Generate features for new data using the fitted pipeline.
        
        Args:
            data: Input OHLCV data
            use_cache: Whether to use cached results (default: self.enable_caching)
            
        Returns:
            Processed features ready for model inference
        """
        if not self.is_fitted:
            raise ValueError("Pipeline must be fitted before generating features")
        
        use_cache = use_cache if use_cache is not None else self.enable_caching
        
        # Check cache
        if use_cache:
            cached_features = self._get_cached_features(data)
            if cached_features is not None:
                self.logger.debug("Using cached features")
                return cached_features
        
        self.logger.info(f"Generating features for {len(data)} records")
        
        with self.performance_benchmarker.monitor_operation("feature_generation", len(data)):
            # Generate raw features
            feature_results = self.orchestrator.generate_all_features(data)
            
            if not feature_results:
                raise ValueError("No features generated")
            
            # Combine features
            combined_features = self.orchestrator.combine_features(feature_results)
            
            if combined_features.empty:
                raise ValueError("Combined features are empty")
            
            # Process features
            processed_features = self.processing_pipeline.transform(combined_features)
            
            # Cache results
            if use_cache:
                self._cache_features(data, processed_features)
            
            self.last_generation_time = datetime.now()
            
            self.logger.info(f"Generated {len(processed_features.columns)} processed features")
        
        return processed_features
    
    def generate_incremental_features(self, new_data: pd.DataFrame, 
                                    existing_features: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        Generate features incrementally for new data points.
        
        Args:
            new_data: New OHLCV data points
            existing_features: Previously generated features (for context)
            
        Returns:
            Features for new data points only
        """
        if not self.is_fitted:
            raise ValueError("Pipeline must be fitted before generating incremental features")
        
        self.logger.info(f"Generating incremental features for {len(new_data)} new records")
        
        # For incremental generation, we need sufficient historical context
        # This is a simplified implementation - in production, you'd maintain a rolling window
        
        if existing_features is not None and len(existing_features) > 0:
            # Use last N records for context (e.g., 200 for technical indicators)
            context_size = min(200, len(existing_features))
            context_data = existing_features.tail(context_size)
            
            # Combine context with new data
            combined_data = pd.concat([context_data, new_data], ignore_index=False)
        else:
            combined_data = new_data
        
        # Generate features for combined data
        all_features = self.generate_features(combined_data, use_cache=False)
        
        # Return only features for new data points
        new_features = all_features.tail(len(new_data))
        
        self.logger.info(f"Generated incremental features: {len(new_features.columns)} features for {len(new_features)} records")
        
        return new_features
    
    def validate_pipeline_health(self) -> Dict[str, Any]:
        """
        Validate the health and performance of the pipeline.
        
        Returns:
            Health check results
        """
        health_report = {
            'timestamp': datetime.now().isoformat(),
            'pipeline_fitted': self.is_fitted,
            'last_generation': self.last_generation_time.isoformat() if self.last_generation_time else None,
            'cache_size': len(self.feature_cache),
            'performance_summary': {},
            'quality_status': 'unknown',
            'recommendations': []
        }
        
        # Performance summary
        if self.enable_performance_monitoring:
            perf_summary = self.performance_benchmarker.get_performance_summary()
            if 'error' not in perf_summary:
                health_report['performance_summary'] = {
                    'total_operations': perf_summary['total_operations'],
                    'mean_duration': perf_summary['duration_stats']['mean'],
                    'mean_throughput': perf_summary['throughput_stats']['mean']
                }
        
        # Health recommendations
        recommendations = []
        
        if not self.is_fitted:
            recommendations.append("Pipeline needs to be fitted before use")
        
        if self.last_generation_time:
            time_since_last = datetime.now() - self.last_generation_time
            if time_since_last > timedelta(hours=1):
                recommendations.append("Pipeline hasn't been used recently - consider warming up")
        
        if len(self.feature_cache) > 1000:
            recommendations.append("Feature cache is large - consider clearing old entries")
        
        health_report['recommendations'] = recommendations
        
        return health_report
    
    def _get_cached_features(self, data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Get cached features if available and valid."""
        if not self.enable_caching:
            return None
        
        # Simple cache key based on data hash (in production, use more sophisticated caching)
        cache_key = hash(str(data.index.tolist() + data.values.flatten().tolist()))
        
        if cache_key in self.feature_cache:
            cached_entry = self.feature_cache[cache_key]
            
            # Check cache age
            cache_age = datetime.now() - cached_entry['timestamp']
            if cache_age < timedelta(hours=self.max_cache_age_hours):
                return cached_entry['features']
            else:
                # Remove expired cache entry
                del self.feature_cache[cache_key]
        
        return None
    
    def _cache_features(self, data: pd.DataFrame, features: pd.DataFrame):
        """Cache generated features."""
        if not self.enable_caching:
            return
        
        cache_key = hash(str(data.index.tolist() + data.values.flatten().tolist()))
        
        self.feature_cache[cache_key] = {
            'timestamp': datetime.now(),
            'features': features.copy()
        }
        
        # Limit cache size
        if len(self.feature_cache) > 100:
            # Remove oldest entries
            oldest_keys = sorted(
                self.feature_cache.keys(),
                key=lambda k: self.feature_cache[k]['timestamp']
            )[:50]
            
            for key in oldest_keys:
                del self.feature_cache[key]
    
    def save_pipeline(self, filepath: str):
        """Save the fitted pipeline to disk."""
        if not self.is_fitted:
            raise ValueError("Pipeline must be fitted before saving")
        
        pipeline_data = {
            'config': self.config.__dict__,
            'feature_config': self.feature_config.__dict__,
            'processing_pipeline': self.processing_pipeline,
            'is_fitted': self.is_fitted,
            'last_generation_time': self.last_generation_time.isoformat() if self.last_generation_time else None,
            'processing_stats': self.processing_stats
        }
        
        joblib.dump(pipeline_data, filepath)
        self.logger.info(f"Pipeline saved to {filepath}")
    
    def load_pipeline(self, filepath: str):
        """Load a fitted pipeline from disk."""
        pipeline_data = joblib.load(filepath)
        
        # Restore configuration
        self.config = Config()
        self.config.__dict__.update(pipeline_data['config'])
        
        self.feature_config = FeatureConfig(self.config)
        self.feature_config.__dict__.update(pipeline_data['feature_config'])
        
        # Restore components
        self.processing_pipeline = pipeline_data['processing_pipeline']
        self.is_fitted = pipeline_data['is_fitted']
        
        if pipeline_data['last_generation_time']:
            self.last_generation_time = datetime.fromisoformat(pipeline_data['last_generation_time'])
        
        self.processing_stats = pipeline_data.get('processing_stats', {})
        
        # Recreate components with restored config
        self.factory = FeatureEngineFactory(self.config, self.feature_config)
        self.orchestrator = FeatureEngineOrchestrator(self.config, self.feature_config)
        
        self.logger.info(f"Pipeline loaded from {filepath}")
    
    def get_feature_importance(self) -> Optional[pd.DataFrame]:
        """Get feature importance analysis from the processing pipeline."""
        if not self.is_fitted or not self.processing_pipeline:
            return None
        
        return self.processing_pipeline.get_feature_importance_analysis()
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """Get processing pipeline summary."""
        if not self.is_fitted or not self.processing_pipeline:
            return {}
        
        return self.processing_pipeline.get_processing_summary()
    
    def clear_cache(self):
        """Clear the feature cache."""
        self.feature_cache.clear()
        self.logger.info("Feature cache cleared")
    
    def get_performance_report(self) -> str:
        """Get comprehensive performance report."""
        if not self.enable_performance_monitoring:
            return "Performance monitoring is disabled"
        
        return self.performance_benchmarker.generate_performance_report()
