"""
Performance Benchmarking and Monitoring Framework

Provides comprehensive performance monitoring for feature engineering including:
- Processing time measurement and analysis
- Memory usage tracking and optimization
- Scalability testing and bottleneck identification
- Performance regression detection
"""

import pandas as pd
import numpy as np
import time
import psutil
import gc
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from contextlib import contextmanager

# Import base classes
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from data_collection.config import Config
from data_collection.error_handling.logger import LoggerMixin


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    operation_name: str
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    memory_before_mb: float
    memory_after_mb: float
    memory_peak_mb: float
    memory_delta_mb: float
    cpu_percent: float
    input_size: int
    output_size: int
    throughput_records_per_second: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'operation_name': self.operation_name,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'duration_seconds': self.duration_seconds,
            'memory_before_mb': self.memory_before_mb,
            'memory_after_mb': self.memory_after_mb,
            'memory_peak_mb': self.memory_peak_mb,
            'memory_delta_mb': self.memory_delta_mb,
            'cpu_percent': self.cpu_percent,
            'input_size': self.input_size,
            'output_size': self.output_size,
            'throughput_records_per_second': self.throughput_records_per_second
        }


class PerformanceBenchmarker(LoggerMixin):
    """
    Comprehensive performance benchmarking and monitoring framework.
    
    Provides detailed performance analysis including timing, memory usage,
    CPU utilization, and throughput measurements for feature engineering operations.
    """
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize performance benchmarker.
        
        Args:
            config: System configuration (optional)
        """
        super().__init__()
        self.config = config or Config()
        
        # Performance history
        self.metrics_history: List[PerformanceMetrics] = []
        
        # Current monitoring state
        self._current_operation = None
        self._start_time = None
        self._memory_before = None
        self._cpu_before = None
        
    @contextmanager
    def monitor_operation(self, operation_name: str, input_size: int = 0):
        """
        Context manager for monitoring operation performance.
        
        Args:
            operation_name: Name of the operation being monitored
            input_size: Size of input data (number of records)
            
        Yields:
            Performance monitoring context
        """
        # Start monitoring
        start_time = datetime.now()
        memory_before = self._get_memory_usage_mb()
        cpu_before = psutil.cpu_percent()
        
        # Force garbage collection for accurate memory measurement
        gc.collect()
        
        self.logger.debug(f"Starting performance monitoring for: {operation_name}")
        
        try:
            yield self
            
        finally:
            # End monitoring
            end_time = datetime.now()
            memory_after = self._get_memory_usage_mb()
            cpu_after = psutil.cpu_percent()
            
            # Calculate metrics
            duration = (end_time - start_time).total_seconds()
            memory_delta = memory_after - memory_before
            cpu_percent = (cpu_before + cpu_after) / 2  # Average CPU usage
            
            # Get peak memory usage (approximation)
            memory_peak = max(memory_before, memory_after)
            
            # Calculate throughput
            throughput = input_size / duration if duration > 0 else 0
            
            # Create metrics object
            metrics = PerformanceMetrics(
                operation_name=operation_name,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                memory_before_mb=memory_before,
                memory_after_mb=memory_after,
                memory_peak_mb=memory_peak,
                memory_delta_mb=memory_delta,
                cpu_percent=cpu_percent,
                input_size=input_size,
                output_size=0,  # Will be updated if provided
                throughput_records_per_second=throughput
            )
            
            # Store metrics
            self.metrics_history.append(metrics)
            
            self.logger.info(
                f"Performance: {operation_name} completed in {duration:.3f}s, "
                f"Memory: {memory_delta:+.1f}MB, Throughput: {throughput:.0f} records/s"
            )
    
    def update_output_size(self, output_size: int):
        """Update the output size for the most recent operation."""
        if self.metrics_history:
            self.metrics_history[-1].output_size = output_size
    
    def benchmark_feature_generation(self, feature_generator: Callable, data: pd.DataFrame, 
                                   operation_name: str = "feature_generation") -> PerformanceMetrics:
        """
        Benchmark a feature generation operation.
        
        Args:
            feature_generator: Function that generates features
            data: Input data
            operation_name: Name of the operation
            
        Returns:
            Performance metrics for the operation
        """
        with self.monitor_operation(operation_name, len(data)):
            result = feature_generator(data)
            
            # Update output size if result is DataFrame
            if isinstance(result, pd.DataFrame):
                self.update_output_size(len(result.columns))
        
        return self.metrics_history[-1]
    
    def benchmark_scalability(self, feature_generator: Callable, base_data: pd.DataFrame,
                            scale_factors: List[float] = None) -> Dict[str, Any]:
        """
        Benchmark scalability by testing with different data sizes.
        
        Args:
            feature_generator: Function that generates features
            base_data: Base dataset to scale
            scale_factors: List of scaling factors to test
            
        Returns:
            Scalability analysis results
        """
        if scale_factors is None:
            scale_factors = [0.1, 0.25, 0.5, 1.0, 2.0]
        
        scalability_results = {
            'scale_factors': scale_factors,
            'metrics': [],
            'analysis': {}
        }
        
        self.logger.info(f"Starting scalability benchmark with {len(scale_factors)} scale factors")
        
        for scale_factor in scale_factors:
            # Create scaled dataset
            if scale_factor <= 1.0:
                # Sample down
                sample_size = int(len(base_data) * scale_factor)
                scaled_data = base_data.sample(n=sample_size, random_state=42)
            else:
                # Replicate data
                replications = int(scale_factor)
                scaled_data = pd.concat([base_data] * replications, ignore_index=True)
            
            # Benchmark the operation
            operation_name = f"scalability_test_{scale_factor}x"
            metrics = self.benchmark_feature_generation(feature_generator, scaled_data, operation_name)
            scalability_results['metrics'].append(metrics.to_dict())
        
        # Analyze scalability
        scalability_results['analysis'] = self._analyze_scalability(scalability_results['metrics'])
        
        return scalability_results
    
    def _analyze_scalability(self, metrics_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze scalability metrics to identify performance characteristics."""
        if len(metrics_list) < 2:
            return {'error': 'Insufficient data for scalability analysis'}
        
        # Extract key metrics
        input_sizes = [m['input_size'] for m in metrics_list]
        durations = [m['duration_seconds'] for m in metrics_list]
        memory_deltas = [m['memory_delta_mb'] for m in metrics_list]
        throughputs = [m['throughput_records_per_second'] for m in metrics_list]
        
        # Calculate scaling characteristics
        analysis = {
            'time_complexity': self._estimate_complexity(input_sizes, durations),
            'memory_complexity': self._estimate_complexity(input_sizes, memory_deltas),
            'throughput_stability': np.std(throughputs) / np.mean(throughputs) if throughputs else 0,
            'performance_degradation': self._calculate_performance_degradation(throughputs),
            'bottleneck_indicators': self._identify_bottlenecks(metrics_list)
        }
        
        return analysis
    
    def _estimate_complexity(self, input_sizes: List[int], metrics: List[float]) -> str:
        """Estimate computational complexity based on scaling behavior."""
        if len(input_sizes) < 3:
            return 'insufficient_data'
        
        # Calculate ratios
        size_ratios = [input_sizes[i] / input_sizes[i-1] for i in range(1, len(input_sizes))]
        metric_ratios = [metrics[i] / metrics[i-1] for i in range(1, len(metrics)) if metrics[i-1] > 0]
        
        if not metric_ratios:
            return 'constant'
        
        avg_size_ratio = np.mean(size_ratios)
        avg_metric_ratio = np.mean(metric_ratios)
        
        # Estimate complexity
        if avg_metric_ratio < avg_size_ratio * 0.5:
            return 'sub_linear'
        elif avg_metric_ratio < avg_size_ratio * 1.5:
            return 'linear'
        elif avg_metric_ratio < avg_size_ratio ** 1.5:
            return 'super_linear'
        else:
            return 'quadratic_or_worse'
    
    def _calculate_performance_degradation(self, throughputs: List[float]) -> float:
        """Calculate performance degradation as throughput decreases with scale."""
        if len(throughputs) < 2:
            return 0.0
        
        # Calculate percentage decrease from first to last
        first_throughput = throughputs[0]
        last_throughput = throughputs[-1]
        
        if first_throughput > 0:
            degradation = (first_throughput - last_throughput) / first_throughput
            return max(0.0, degradation)
        
        return 0.0
    
    def _identify_bottlenecks(self, metrics_list: List[Dict[str, Any]]) -> List[str]:
        """Identify potential performance bottlenecks."""
        bottlenecks = []
        
        # Check for memory bottlenecks
        memory_deltas = [m['memory_delta_mb'] for m in metrics_list]
        if any(delta > 1000 for delta in memory_deltas):  # >1GB memory increase
            bottlenecks.append('high_memory_usage')
        
        # Check for CPU bottlenecks
        cpu_usages = [m['cpu_percent'] for m in metrics_list]
        if any(cpu > 80 for cpu in cpu_usages):
            bottlenecks.append('high_cpu_usage')
        
        # Check for throughput degradation
        throughputs = [m['throughput_records_per_second'] for m in metrics_list]
        if len(throughputs) >= 2:
            degradation = self._calculate_performance_degradation(throughputs)
            if degradation > 0.5:  # >50% degradation
                bottlenecks.append('throughput_degradation')
        
        # Check for long processing times
        durations = [m['duration_seconds'] for m in metrics_list]
        if any(duration > 300 for duration in durations):  # >5 minutes
            bottlenecks.append('long_processing_time')
        
        return bottlenecks
    
    def _get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB."""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        if not self.metrics_history:
            return {'error': 'No performance data available'}
        
        # Convert to DataFrame for analysis
        metrics_data = [m.to_dict() for m in self.metrics_history]
        df = pd.DataFrame(metrics_data)
        
        summary = {
            'total_operations': len(self.metrics_history),
            'time_range': {
                'start': df['start_time'].min(),
                'end': df['end_time'].max()
            },
            'duration_stats': {
                'mean': float(df['duration_seconds'].mean()),
                'median': float(df['duration_seconds'].median()),
                'std': float(df['duration_seconds'].std()),
                'min': float(df['duration_seconds'].min()),
                'max': float(df['duration_seconds'].max())
            },
            'memory_stats': {
                'mean_delta': float(df['memory_delta_mb'].mean()),
                'max_delta': float(df['memory_delta_mb'].max()),
                'total_delta': float(df['memory_delta_mb'].sum())
            },
            'throughput_stats': {
                'mean': float(df['throughput_records_per_second'].mean()),
                'median': float(df['throughput_records_per_second'].median()),
                'max': float(df['throughput_records_per_second'].max())
            },
            'operation_breakdown': df.groupby('operation_name').agg({
                'duration_seconds': ['count', 'mean', 'sum'],
                'memory_delta_mb': 'sum',
                'throughput_records_per_second': 'mean'
            }).to_dict()
        }
        
        return summary
    
    def generate_performance_report(self) -> str:
        """Generate human-readable performance report."""
        summary = self.get_performance_summary()
        
        if 'error' in summary:
            return f"Performance Report: {summary['error']}"
        
        report = []
        report.append("=" * 60)
        report.append("PERFORMANCE BENCHMARKING REPORT")
        report.append("=" * 60)
        
        # Overall statistics
        report.append(f"Total Operations: {summary['total_operations']}")
        report.append(f"Time Range: {summary['time_range']['start']} to {summary['time_range']['end']}")
        report.append("")
        
        # Duration statistics
        duration_stats = summary['duration_stats']
        report.append("PROCESSING TIME ANALYSIS:")
        report.append(f"  Mean Duration: {duration_stats['mean']:.3f}s")
        report.append(f"  Median Duration: {duration_stats['median']:.3f}s")
        report.append(f"  Range: {duration_stats['min']:.3f}s - {duration_stats['max']:.3f}s")
        report.append("")
        
        # Memory statistics
        memory_stats = summary['memory_stats']
        report.append("MEMORY USAGE ANALYSIS:")
        report.append(f"  Mean Memory Delta: {memory_stats['mean_delta']:+.1f}MB")
        report.append(f"  Max Memory Delta: {memory_stats['max_delta']:+.1f}MB")
        report.append(f"  Total Memory Impact: {memory_stats['total_delta']:+.1f}MB")
        report.append("")
        
        # Throughput statistics
        throughput_stats = summary['throughput_stats']
        report.append("THROUGHPUT ANALYSIS:")
        report.append(f"  Mean Throughput: {throughput_stats['mean']:.0f} records/second")
        report.append(f"  Median Throughput: {throughput_stats['median']:.0f} records/second")
        report.append(f"  Peak Throughput: {throughput_stats['max']:.0f} records/second")
        
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def clear_history(self):
        """Clear performance metrics history."""
        self.metrics_history.clear()
        self.logger.info("Performance metrics history cleared")
