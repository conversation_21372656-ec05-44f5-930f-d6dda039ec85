"""
Market Regime Detection Engine

Generates comprehensive market regime detection features including:
- Volatility regime classification
- Trend regime identification
- Correlation regime analysis
- Regime transition detection
- Multi-dimensional regime clustering
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

# Import base classes
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from features.core.base_feature import BaseFeatureEngine, FeatureResult
from features.config.feature_config import FeatureConfig
from data_collection.config import Config


class RegimeDetection(BaseFeatureEngine):
    """
    Market regime detection engine.
    
    Identifies different market regimes based on volatility, trend, and correlation patterns
    to provide context for reinforcement learning models.
    """
    
    def __init__(self, config: Config, feature_config: FeatureConfig, **kwargs):
        """
        Initialize regime detection engine.
        
        Args:
            config: Main system configuration
            feature_config: Feature-specific configuration
            **kwargs: Additional parameters
        """
        super().__init__(config)
        self.feature_config = feature_config
        self.regime_config = feature_config.regime_detection
        
        # Volatility regime configuration
        self.vol_regime_config = self.regime_config.get('volatility', {})
        self.vol_windows = self.vol_regime_config.get('windows', [20, 50])
        self.vol_thresholds = self.vol_regime_config.get('thresholds', [0.3, 0.7])
        
        # Trend regime configuration
        self.trend_regime_config = self.regime_config.get('trend', {})
        self.trend_windows = self.trend_regime_config.get('windows', [20, 50, 100])
        self.trend_threshold = self.trend_regime_config.get('threshold', 0.1)
        
        # Clustering configuration
        self.clustering_config = self.regime_config.get('clustering', {})
        self.n_regimes = self.clustering_config.get('n_regimes', 4)
        self.clustering_window = self.clustering_config.get('window', 100)
        
        # Regime transition configuration
        self.transition_config = self.regime_config.get('transitions', {})
        self.transition_smoothing = self.transition_config.get('smoothing', 5)
    
    def get_feature_type(self) -> str:
        """Get the type of features this engine generates."""
        return "regime_detection"
    
    def get_required_columns(self) -> List[str]:
        """Get required columns for regime detection."""
        return ['open', 'high', 'low', 'close']
    
    def get_feature_names(self) -> List[str]:
        """Get list of feature names this engine generates."""
        feature_names = []
        
        # Volatility regime features
        for window in self.vol_windows:
            feature_names.extend([
                f"vol_regime_{window}",
                f"vol_regime_strength_{window}",
                f"vol_regime_duration_{window}"
            ])
        
        # Trend regime features
        for window in self.trend_windows:
            feature_names.extend([
                f"trend_regime_{window}",
                f"trend_strength_{window}",
                f"trend_consistency_{window}"
            ])
        
        # Multi-dimensional regime features
        feature_names.extend([
            "market_regime_cluster",
            "regime_stability",
            "regime_transition_probability"
        ])
        
        # Regime transition features
        feature_names.extend([
            "regime_change_signal",
            "regime_persistence",
            "regime_momentum"
        ])
        
        # Combined regime features
        feature_names.extend([
            "overall_market_regime",
            "regime_confidence",
            "regime_divergence"
        ])
        
        return feature_names
    
    def _generate_features_impl(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Implementation-specific feature generation logic.
        
        Args:
            data: Input OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with generated regime detection features
        """
        # Ensure datetime index
        data = self._ensure_datetime_index(data)
        
        # Initialize features DataFrame
        features = pd.DataFrame(index=data.index)
        
        # Generate volatility regime features
        vol_regime_features = self._generate_volatility_regimes(data)
        features = pd.concat([features, vol_regime_features], axis=1)
        
        # Generate trend regime features
        trend_regime_features = self._generate_trend_regimes(data)
        features = pd.concat([features, trend_regime_features], axis=1)
        
        # Generate multi-dimensional regime features
        multi_regime_features = self._generate_multi_dimensional_regimes(data, vol_regime_features, trend_regime_features)
        features = pd.concat([features, multi_regime_features], axis=1)
        
        # Generate regime transition features
        transition_features = self._generate_regime_transitions(features)
        features = pd.concat([features, transition_features], axis=1)
        
        # Generate combined regime features
        combined_features = self._generate_combined_regimes(features)
        features = pd.concat([features, combined_features], axis=1)
        
        self.logger.info(f"Generated {len(features.columns)} regime detection features")
        return features
    
    def _generate_volatility_regimes(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate volatility-based regime features."""
        vol_df = pd.DataFrame(index=data.index)
        
        # Calculate returns
        returns = data['close'].pct_change()
        
        for window in self.vol_windows:
            try:
                # Rolling volatility
                volatility = returns.rolling(window=window).std()
                
                # Volatility percentiles for regime classification
                vol_percentile = volatility.rolling(window=window*4).rank(pct=True)
                
                # Classify volatility regimes
                vol_regime = pd.Series(0, index=data.index)  # 0 = normal
                vol_regime[vol_percentile > self.vol_thresholds[1]] = 2  # 2 = high volatility
                vol_regime[vol_percentile < self.vol_thresholds[0]] = 1  # 1 = low volatility
                vol_df[f"vol_regime_{window}"] = vol_regime
                
                # Volatility regime strength
                vol_strength = np.abs(vol_percentile - 0.5) * 2  # 0-1 scale
                vol_df[f"vol_regime_strength_{window}"] = vol_strength
                
                # Regime duration (how long in current regime)
                regime_changes = (vol_regime.diff() != 0).cumsum()
                regime_duration = regime_changes.groupby(regime_changes).cumcount() + 1
                vol_df[f"vol_regime_duration_{window}"] = regime_duration
                
            except Exception as e:
                self.logger.warning(f"Error calculating volatility regime for window {window}: {str(e)}")
        
        self.logger.debug(f"Generated {len(vol_df.columns)} volatility regime features")
        return vol_df
    
    def _generate_trend_regimes(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate trend-based regime features."""
        trend_df = pd.DataFrame(index=data.index)
        
        for window in self.trend_windows:
            try:
                # Calculate trend using linear regression slope
                close_prices = data['close']
                
                # Rolling trend slope
                trend_slope = close_prices.rolling(window=window).apply(
                    lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == window else np.nan,
                    raw=True
                )
                
                # Normalize trend slope
                trend_slope_normalized = trend_slope / close_prices.rolling(window=window).mean()
                
                # Classify trend regimes
                trend_regime = pd.Series(0, index=data.index)  # 0 = sideways
                trend_regime[trend_slope_normalized > self.trend_threshold] = 1  # 1 = uptrend
                trend_regime[trend_slope_normalized < -self.trend_threshold] = -1  # -1 = downtrend
                trend_df[f"trend_regime_{window}"] = trend_regime
                
                # Trend strength
                trend_strength = np.abs(trend_slope_normalized)
                trend_df[f"trend_strength_{window}"] = trend_strength
                
                # Trend consistency (how consistent the trend direction is)
                price_changes = close_prices.diff()
                trend_consistency = (
                    (price_changes > 0).rolling(window=window).mean() 
                    if trend_slope_normalized.iloc[-1] > 0 
                    else (price_changes < 0).rolling(window=window).mean()
                )
                trend_df[f"trend_consistency_{window}"] = trend_consistency
                
            except Exception as e:
                self.logger.warning(f"Error calculating trend regime for window {window}: {str(e)}")
        
        self.logger.debug(f"Generated {len(trend_df.columns)} trend regime features")
        return trend_df
    
    def _generate_multi_dimensional_regimes(self, data: pd.DataFrame, vol_features: pd.DataFrame, 
                                          trend_features: pd.DataFrame) -> pd.DataFrame:
        """Generate multi-dimensional regime features using clustering."""
        multi_df = pd.DataFrame(index=data.index)
        
        try:
            # Prepare features for clustering
            clustering_features = []
            
            # Add volatility features
            vol_cols = [col for col in vol_features.columns if 'regime_' in col and 'duration' not in col]
            if vol_cols:
                clustering_features.extend(vol_cols)
            
            # Add trend features
            trend_cols = [col for col in trend_features.columns if 'regime_' in col]
            if trend_cols:
                clustering_features.extend(trend_cols)
            
            if clustering_features:
                # Combine features
                feature_data = pd.concat([vol_features[vol_cols], trend_features[trend_cols]], axis=1)
                feature_data = feature_data.dropna()
                
                if len(feature_data) > self.clustering_window:
                    # OPTIMIZED: Apply clustering less frequently for better performance
                    regime_clusters = pd.Series(np.nan, index=data.index)
                    regime_stability = pd.Series(np.nan, index=data.index)

                    # Reduce clustering frequency - only cluster every 20 points instead of every point
                    clustering_step = 20
                    last_kmeans = None
                    last_scaler = None

                    for i in range(self.clustering_window, len(feature_data), clustering_step):
                        try:
                            # Get window data
                            window_data = feature_data.iloc[i-self.clustering_window:i]

                            # Standardize features
                            scaler = StandardScaler()
                            scaled_data = scaler.fit_transform(window_data)

                            # Apply K-means clustering with reduced iterations for speed
                            kmeans = KMeans(n_clusters=self.n_regimes, random_state=42, n_init=3, max_iter=100)
                            clusters = kmeans.fit_predict(scaled_data)

                            # Store for interpolation
                            last_kmeans = kmeans
                            last_scaler = scaler

                            # Assign regime for this window
                            current_regime = clusters[-1]

                            # Fill regime values for the entire step window
                            end_idx = min(i + clustering_step, len(feature_data))
                            for j in range(i, end_idx):
                                if j < len(feature_data):
                                    regime_clusters.iloc[feature_data.index[j]] = current_regime

                            # Calculate regime stability (simplified)
                            recent_regimes = clusters[-10:]  # Last 10 observations (reduced from 20)
                            stability = (recent_regimes == current_regime).mean()

                            # Fill stability values for the entire step window
                            for j in range(i, end_idx):
                                if j < len(feature_data):
                                    regime_stability.iloc[feature_data.index[j]] = stability

                        except Exception as e:
                            self.logger.debug(f"Error in clustering at index {i}: {str(e)}")
                            # Use previous regime if available
                            if not regime_clusters.isna().all():
                                last_regime = regime_clusters.dropna().iloc[-1] if len(regime_clusters.dropna()) > 0 else 0
                                end_idx = min(i + clustering_step, len(feature_data))
                                for j in range(i, end_idx):
                                    if j < len(feature_data):
                                        regime_clusters.iloc[feature_data.index[j]] = last_regime
                            continue
                    
                    # Reindex to original data
                    multi_df['market_regime_cluster'] = regime_clusters.reindex(data.index)
                    multi_df['regime_stability'] = regime_stability.reindex(data.index)
                    
                    # Regime transition probability
                    regime_changes = (multi_df['market_regime_cluster'].diff() != 0).rolling(window=50).mean()
                    multi_df['regime_transition_probability'] = regime_changes
            
        except Exception as e:
            self.logger.warning(f"Error calculating multi-dimensional regimes: {str(e)}")
        
        self.logger.debug(f"Generated {len(multi_df.columns)} multi-dimensional regime features")
        return multi_df

    def _generate_regime_transitions(self, features: pd.DataFrame) -> pd.DataFrame:
        """Generate regime transition features."""
        transition_df = pd.DataFrame(index=features.index)

        try:
            # Find regime columns
            regime_cols = [col for col in features.columns if 'regime' in col and 'duration' not in col and 'strength' not in col]

            if regime_cols:
                # Regime change signal (any regime changed)
                regime_changes = pd.DataFrame()
                for col in regime_cols:
                    if col in features.columns:
                        regime_changes[col] = (features[col].diff() != 0).astype(int)

                if not regime_changes.empty:
                    # Overall regime change signal
                    transition_df['regime_change_signal'] = regime_changes.max(axis=1)

                    # Regime persistence (how long since last change)
                    change_points = transition_df['regime_change_signal'].cumsum()
                    regime_persistence = change_points.groupby(change_points).cumcount() + 1
                    transition_df['regime_persistence'] = regime_persistence

                    # Regime momentum (rate of regime changes)
                    regime_momentum = transition_df['regime_change_signal'].rolling(window=20).mean()
                    transition_df['regime_momentum'] = regime_momentum

        except Exception as e:
            self.logger.warning(f"Error calculating regime transitions: {str(e)}")

        self.logger.debug(f"Generated {len(transition_df.columns)} regime transition features")
        return transition_df

    def _generate_combined_regimes(self, features: pd.DataFrame) -> pd.DataFrame:
        """Generate combined regime features."""
        combined_df = pd.DataFrame(index=features.index)

        try:
            # Overall market regime (combination of vol and trend regimes)
            vol_regime_cols = [col for col in features.columns if 'vol_regime_' in col and 'strength' not in col and 'duration' not in col]
            trend_regime_cols = [col for col in features.columns if 'trend_regime_' in col]

            if vol_regime_cols and trend_regime_cols:
                # Use the medium-term regimes
                vol_regime = features[vol_regime_cols[0]] if vol_regime_cols else 0
                trend_regime = features[trend_regime_cols[1]] if len(trend_regime_cols) > 1 else features[trend_regime_cols[0]]

                # Combine regimes into overall market regime
                # 0: Low vol + sideways, 1: Low vol + trend, 2: High vol + sideways, 3: High vol + trend
                overall_regime = vol_regime * 2 + (trend_regime != 0).astype(int)
                combined_df['overall_market_regime'] = overall_regime

                # Regime confidence (how aligned different regime indicators are)
                regime_agreement = pd.Series(0.0, index=features.index)

                # Check agreement between different timeframe regimes
                if len(vol_regime_cols) > 1:
                    vol_agreement = (features[vol_regime_cols[0]] == features[vol_regime_cols[1]]).astype(float)
                    regime_agreement += vol_agreement * 0.5

                if len(trend_regime_cols) > 1:
                    trend_agreement = (features[trend_regime_cols[0]] == features[trend_regime_cols[1]]).astype(float)
                    regime_agreement += trend_agreement * 0.5

                combined_df['regime_confidence'] = regime_agreement

                # Regime divergence (disagreement between regime types)
                if 'market_regime_cluster' in features.columns:
                    cluster_regime = features['market_regime_cluster']
                    # Measure divergence between cluster-based and rule-based regimes
                    regime_divergence = np.abs(cluster_regime - overall_regime) / self.n_regimes
                    combined_df['regime_divergence'] = regime_divergence

        except Exception as e:
            self.logger.warning(f"Error calculating combined regimes: {str(e)}")

        self.logger.debug(f"Generated {len(combined_df.columns)} combined regime features")
        return combined_df
