2025-09-20 11:47:40,513 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 11:47:40,546 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 11:47:40,554 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 11:47:40,560 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 11:47:40,561 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 11:47:40,573 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 11:47:40,581 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 11:57:20,854 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 11:57:20,884 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 11:57:20,894 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 11:57:20,901 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 11:57:20,902 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 11:57:20,914 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 11:57:20,925 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:02:19,258 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:02:19,259 - TradingSessionFeatures - ERROR - generate_features:181 - Error generating trading_sessions features: Missing required columns: ['tick_volume']
2025-09-20 12:04:35,003 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:04:35,035 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:04:35,040 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:04:35,045 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:04:35,048 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:04:35,062 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:04:35,073 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:05:14,174 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:05:14,204 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:05:14,212 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:05:14,217 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:05:14,218 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:05:14,237 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:05:14,245 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:05:50,683 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:05:50,716 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:05:50,721 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:05:50,727 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:05:50,729 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:05:50,741 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:05:50,751 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:06:53,641 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:06:53,669 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:06:53,674 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:06:53,680 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:06:53,682 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:06:53,693 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:06:53,705 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:07:28,407 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:07:28,439 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:07:28,445 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:07:28,454 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:07:28,455 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:07:28,467 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:07:28,479 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:08:28,941 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:08:28,976 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:08:28,983 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:08:28,989 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:08:28,992 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:08:29,003 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:08:29,018 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:09:11,402 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:09:11,438 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:09:11,444 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:09:11,450 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:09:11,451 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:09:11,461 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:09:11,470 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:09:49,307 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:09:49,337 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:09:49,344 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:09:49,349 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:09:49,351 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:09:49,362 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:09:49,376 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:10:24,426 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:10:24,455 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:10:24,464 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:10:24,469 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:10:24,471 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:10:24,481 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:10:24,493 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:12:09,110 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:12:09,145 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:12:09,151 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:12:09,156 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:12:09,157 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:12:09,166 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:12:09,174 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:12:48,464 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:12:48,495 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:12:48,501 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:12:48,507 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:12:48,509 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:12:48,518 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:12:48,531 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:13:21,481 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:13:21,514 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:13:21,520 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:13:21,529 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:13:21,530 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:13:21,538 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:13:21,548 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:14:29,807 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:14:29,836 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:14:29,843 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:14:29,850 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:14:29,851 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:14:29,860 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:14:29,872 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:15:47,054 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:15:47,083 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:15:47,088 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:15:47,096 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:15:47,097 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:15:47,107 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:15:47,117 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:16:34,567 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:16:34,596 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:16:34,602 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:16:34,608 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:16:34,610 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:16:34,619 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:16:34,631 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:16:47,668 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:16:47,700 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:16:47,706 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:16:47,711 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:16:47,712 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:16:47,723 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 19, 'session_asian_volatility_ratio': 20, 'session_european_volume_ratio': 91, 'session_european_volatility_ratio': 91, 'session_us_volume_ratio': 163, 'session_us_volatility_ratio': 163}
2025-09-20 12:16:47,734 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:16:50,920 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:16:50,936 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:16:50,938 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:16:50,940 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:16:50,941 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:16:50,944 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 163, 'session_asian_volatility_ratio': 163, 'session_european_volume_ratio': 19, 'session_european_volatility_ratio': 20, 'session_us_volume_ratio': 31, 'session_us_volatility_ratio': 31}
2025-09-20 12:16:50,952 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
2025-09-20 12:16:53,561 - TradingSessionFeatures - INFO - generate_features:153 - Starting trading_sessions feature generation
2025-09-20 12:16:53,576 - TradingSessionFeatures - WARNING - _generate_temporal_features:236 - Error calculating temporal features: 'numpy.ndarray' object has no attribute 'days'
2025-09-20 12:16:53,578 - TradingSessionFeatures - WARNING - _generate_transition_features:303 - Error calculating transition features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:16:53,580 - TradingSessionFeatures - WARNING - _generate_timing_features:386 - Error calculating timing features: 'numpy.ndarray' object has no attribute 'map'
2025-09-20 12:16:53,581 - TradingSessionFeatures - INFO - _generate_features_impl:157 - Generated 20 trading session features
2025-09-20 12:16:53,586 - TradingSessionFeatures - WARNING - _validate_generated_features:236 - NaN values in features: {'session_asian_volume_ratio': 198, 'session_asian_volatility_ratio': 199, 'session_european_volume_ratio': 19, 'session_european_volatility_ratio': 20, 'session_us_volume_ratio': 83, 'session_us_volatility_ratio': 83}
2025-09-20 12:16:53,594 - TradingSessionFeatures - INFO - generate_features:177 - Generated 20 trading_sessions features
